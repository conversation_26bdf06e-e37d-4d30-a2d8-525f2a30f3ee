import type {
  CaseWorkflowStepStatus,
  WorkflowStep,
  WorkflowStepTask,
} from '../../schema/types';
import type { TaskWithRelations } from './tasks';

export type GetWorkflowStepTasksResult = {
  template: WorkflowStepTask;
  actualTask: TaskWithRelations | null;
  hasActualTask: boolean;
};

export type WorkflowStepWithStatus = Omit<
  WorkflowStep,
  'createdAt' | 'workflowId'
> &
  Pick<
    CaseWorkflowStepStatus,
    'status' | 'startedAt' | 'completedAt' | 'notes'
  >;
