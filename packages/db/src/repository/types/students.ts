import type { Student } from '@lilypad/db/schema';

export interface GetStudentsParams {
  page?: number;
  perPage?: number;
  search?: string;
  filters?: Array<{
    id: string;
    value: unknown;
    isMulti?: boolean;
  }>;
  joinOperator?: 'and' | 'or';
  sort?: Array<{
    id: string;
    desc: boolean;
  }>;
}

export interface GetStudentsResult {
  data: StudentWithSchoolAndDistrict[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface StudentWithSchoolAndDistrict extends Student {
  schoolName: string | null;
  districtName: string | null;
  schoolId: string | null;
  districtId: string | null;
  dateOfConsent: string | null;
}

export type StudentSortField =
  | 'fullName'
  | 'studentIdNumber'
  | 'grade'
  | 'gender'
  | 'enrollmentStatus'
  | 'schoolName'
  | 'districtName'
  | 'dateOfBirth';

export type StudentFilterField =
  | 'fullName'
  | 'studentIdNumber'
  | 'grade'
  | 'gender'
  | 'enrollmentStatus'
  | 'emergencyContactName'
  | 'emergencyContactPhone'
  | 'dateOfBirth'
  | 'schoolName'
  | 'districtName';
