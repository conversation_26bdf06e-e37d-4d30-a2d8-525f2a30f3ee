import { eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { CaseWorkflowStatusEnum, TaskStatusEnum } from '../schema/enums';
import { caseWorkflowStepStatusesTable } from '../schema/tables';
import { WorkflowStepsRepository } from './workflow-steps';

export class WorkflowsRepository {
  private readonly tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async canCompleteStepBasedOnTasks(
    caseId: string,
    stepId: string
  ): Promise<{
    canComplete: boolean;
    completedRequired: number;
    totalRequired: number;
    missingRequiredTasks: string[];
  }> {
    const stepsRepo = new WorkflowStepsRepository(this.tx);
    const stepData = await stepsRepo.getWorkflowStepTasks(stepId, caseId);

    const requiredTemplates = stepData.filter(
      (item) => item.template.isRequired
    );
    const completedRequired = requiredTemplates.filter(
      (item) => item.actualTask?.status === TaskStatusEnum.COMPLETED
    ).length;

    const missingRequiredTasks = requiredTemplates
      .filter(
        (item) =>
          !item.actualTask ||
          item.actualTask.status !== TaskStatusEnum.COMPLETED
      )
      .map((item) => item.template.taskType);

    return {
      canComplete: completedRequired === requiredTemplates.length,
      completedRequired,
      totalRequired: requiredTemplates.length,
      missingRequiredTasks,
    };
  }

  async completeWorkflowStep(
    caseId: string,
    stepId: string
  ): Promise<{
    success: boolean;
    message: string;
    stepCompleted: boolean;
  }> {
    const canCompleteResult = await this.canCompleteStepBasedOnTasks(
      caseId,
      stepId
    );

    if (!canCompleteResult.canComplete) {
      return {
        success: false,
        message: `Cannot complete step. ${canCompleteResult.totalRequired - canCompleteResult.completedRequired} required tasks are still pending.`,
        stepCompleted: false,
      };
    }

    await this.tx
      .update(caseWorkflowStepStatusesTable)
      .set({ status: CaseWorkflowStatusEnum.COMPLETED })
      .where(eq(caseWorkflowStepStatusesTable.id, stepId));

    return {
      success: true,
      message: 'Workflow step completed successfully.',
      stepCompleted: true,
    };
  }
}
