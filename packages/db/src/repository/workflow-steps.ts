import { eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { workflowStepTasksTable } from '../schema/tables';
import { TasksRepository } from './tasks';
import type { TaskWithRelations } from './types/tasks';
import type { GetWorkflowStepTasksResult } from './types/workflow-steps';

export class WorkflowStepsRepository {
  private readonly tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async getWorkflowStepTasks(
    workflowStepId: string,
    caseId?: string
  ): Promise<GetWorkflowStepTasksResult[]> {
    const templates = await this.tx
      .select()
      .from(workflowStepTasksTable)
      .where(eq(workflowStepTasksTable.workflowStepId, workflowStepId))
      .orderBy(workflowStepTasksTable.orderIndex);

    let actualTasks: TaskWithRelations[] = [];
    if (caseId) {
      const tasksRepo = new TasksRepository(this.tx);
      actualTasks = await tasksRepo.getTasksByWorkflowStep(
        caseId,
        workflowStepId
      );
    }

    return templates.map((template) => {
      const actualTask = actualTasks.find(
        (task) => task.taskType === template.taskType
      );

      return {
        template,
        actualTask: actualTask || null,
        hasActualTask: !!actualTask,
      };
    });
  }
}
