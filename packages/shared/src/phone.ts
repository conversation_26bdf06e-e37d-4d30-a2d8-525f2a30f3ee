import {
  isValidPhoneNumber,
  parsePhoneNumberFromString,
} from 'libphonenumber-js';

const FORMAT_3 = /(\d{3})/;
const FORMAT_6 = /(\d{3})(\d{3})/;
const FORMAT_10 = /(\d{3})(\d{3})(\d{4})/;

export function formatUSPhone(input = ''): string {
  if (input.length > 10) {
    return input;
  }
  const numericInput = input.replace(/\D/g, '');
  const formats = [
    { length: 4, format: FORMAT_3, replace: '($1)' },
    { length: 6, format: FORMAT_6, replace: '($1) $2' },
    { length: 10, format: FORMAT_10, replace: '($1) $2-$3' },
  ];

  for (const { length, format, replace } of formats) {
    if (numericInput.length < length) {
      return numericInput;
    }
    if (numericInput.length === length) {
      return numericInput.replace(format, replace);
    }
  }

  return numericInput.slice(0, 10);
}

const FORMAT_PHONE = /^\+?1?(\d{3})(\d{3})(\d{4})$/;
export function displayPhoneNumber(phoneNumber?: string): string {
  if (!phoneNumber) {
    return '';
  }
  try {
    const parsed = parsePhoneNumberFromString(phoneNumber, 'US');
    if (parsed) {
      return parsed.formatNational();
    }
  } catch {
    //
  }

  return phoneNumber.replace(FORMAT_PHONE, '($1) $2-$3');
}

export function validatePhone(phoneNumber = ''): boolean {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return false;
  }
  if (isValidPhoneNumber(phoneNumber, 'US')) {
    return true;
  }
  return isValidPhoneNumber(phoneNumber);
}

/**
 * Normalizes US phone numbers to E.164 format (+1XXXXXXXXXX)
 * Handles various input formats and returns consistent output
 */
export function normalizePhone(phoneNumber = ''): string {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return '';
  }

  try {
    let parsed = parsePhoneNumberFromString(phoneNumber, 'US');
    if (!parsed?.isValid()) {
      parsed = parsePhoneNumberFromString(phoneNumber);
    }
    if (parsed?.isValid() && parsed.country === 'US') {
      return parsed.format('E.164');
    }
  } catch {
    //
  }

  return phoneNumber;
}
