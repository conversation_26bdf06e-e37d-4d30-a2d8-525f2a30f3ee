import type { TransactionType } from '@lilypad/db/client';
import {
  Case<PERSON><PERSON>rityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
  DocumentCategoryEnum,
  IepStatusEnum,
} from '@lilypad/db/enums';
import {
  CaseRepository,
  type CreateParentData,
  DocumentsRepository,
  ParentsRepository,
  StudentEnrollmentsRepository,
  StudentLanguagesRepository,
  StudentRepository,
} from '@lilypad/db/repository/index';
import type {
  NewCase,
  NewStudentEnrollment,
  NewStudentLanguage,
  NewStudentParent,
  Parent,
} from '@lilypad/db/schema/types';
import { serializableToFile } from '@lilypad/shared/lib/file-utils';
import { logger } from '@lilypad/shared/logger';
import type { AuthUser } from '@lilypad/supabase';
import { uploadStudentDocumentServer } from '@lilypad/supabase/storage/students/documents.server';
import {
  type AddStudentInput,
  type AddStudentResult,
  type BulkCreateStudentsInput,
  type BulkStudentInput,
  type DocumentInput,
  isSerializableFile,
  type ParentInput,
  type StudentInput,
  type StudentSaveResult,
} from './types';

const BATCH_SIZE = 10; // Reduced batch size to prevent transaction timeouts

export class StudentsService {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createStudent(
    input: AddStudentInput,
    user: AuthUser
  ): Promise<AddStudentResult> {
    try {
      const studentRepository = new StudentRepository(this.tx);
      const parentsRepository = new ParentsRepository(this.tx);
      const enrollmentsRepository = new StudentEnrollmentsRepository(this.tx);
      const languagesRepository = new StudentLanguagesRepository(this.tx);
      const caseRepository = new CaseRepository(this.tx);

      const existingStudent =
        await studentRepository.getStudentByStudentIdNumber(
          input.studentIdNumber
        );

      if (existingStudent) {
        throw new Error(
          `Student with ID "${input.studentIdNumber}" already exists in the system. Please use a different Student ID.`
        );
      }

      // Validate out-of-district and private school relationship
      if (input.outOfDistrict && !input.privateSchool?.trim()) {
        throw new Error(
          'Private school name is required when student is out of district'
        );
      }

      // Create student data
      const studentData = {
        studentIdNumber: input.studentIdNumber,
        firstName: input.firstName,
        lastName: input.lastName,
        preferredName: input.preferredName,
        dateOfBirth: input.dateOfBirth.toISOString().split('T')[0],
        gender: input.gender,
        grade: input.grade,
        primarySchoolId: input.primarySchoolId,
        privateSchool: input.outOfDistrict ? input.privateSchool : null,
      };

      // Create student using repository method
      const insertedStudent =
        await studentRepository.createStudent(studentData);
      const studentId = insertedStudent.id;

      // Create enrollment
      const enrollment: NewStudentEnrollment = {
        studentId,
        schoolId: input.primarySchoolId,
      };

      await enrollmentsRepository.createEnrollments([enrollment]);

      // Create languages
      const languages: NewStudentLanguage[] = input.languageIds.map(
        (languageId) => ({
          studentId,
          languageId,
          isPrimary: languageId === input.primaryLanguageId,
        })
      );

      await languagesRepository.createStudentLanguages(languages);

      // Process parents
      const allParentsData = new Map<string, ParentInput>();
      for (const parent of input.parents) {
        const parentKey = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
        allParentsData.set(parentKey, parent);
      }

      const existingParentMap = await this.processParents(
        parentsRepository,
        allParentsData
      );

      // Create student-parent relationships
      const parentRelationships: NewStudentParent[] = [];
      for (const parent of input.parents) {
        const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
        const parentId = existingParentMap.get(key);

        if (parentId) {
          parentRelationships.push({
            studentId,
            parentId,
            isPrimaryContact: parent.isPrimaryContact,
            hasPickupAuthorization: parent.hasPickupAuthorization,
          });
        }
      }

      await parentsRepository.createStudentParentRelationships(
        parentRelationships
      );

      let caseId: string | undefined;

      // Create case
      const now = new Date();
      const futureDate = new Date(now);
      futureDate.setFullYear(futureDate.getFullYear() + 1); // Set IEP end date to 1 year from now
      const isCaseActive =
        input.documents?.some(
          (doc) => doc.category === DocumentCategoryEnum.BACKGROUND
        ) ?? false;

      const caseData: NewCase = {
        studentId,
        status: CaseStatusEnum.READY_FOR_EVALUATION,
        priority: input.caseInfo.priority || CasePriorityEnum.MEDIUM,
        caseType: input.caseInfo.caseType || CaseTypeEnum.INITIAL_EVALUATION,
        dateOfConsent: input.caseInfo.dateOfConsent,
        isActive: isCaseActive,
        iepStatus: IepStatusEnum.INACTIVE, // Start as inactive, will be activated later
        iepStartDate: now,
        iepEndDate: futureDate,
      };

      // Create case using repository method
      await caseRepository.createCase(caseData);

      // Get the created case to get the ID
      const createdCase = await caseRepository.getCaseByStudentId(studentId);

      if (createdCase) {
        caseId = createdCase.id;

        // Store initial notes if provided using repository method
        if (input.caseInfo.initialNotes || input.notes?.content) {
          const notesContent =
            input.caseInfo.initialNotes || JSON.stringify(input.notes?.content);

          await caseRepository.createCaseDetail(
            caseId,
            'initial_notes',
            notesContent
          );
        }
      }

      // Handle document uploads asynchronously
      if (input.documents && input.documents.length > 0) {
        const documentsToUpload = input.documents.map((doc) => ({
          studentId,
          tempId: input.id,
          doc,
        }));

        this.handleDocumentUploads(documentsToUpload, user.id);
      }

      return {
        success: true,
        studentId,
        caseId,
      };
    } catch (error) {
      const errorMessage = this.parseDbError(error);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Fix later
  async bulkCreateStudents(
    input: BulkCreateStudentsInput,
    user: AuthUser
  ): Promise<StudentSaveResult[]> {
    const results: StudentSaveResult[] = [];
    const totalStudents = input.students.length;

    // Initialize repositories
    const studentRepository = new StudentRepository(this.tx);
    const parentsRepository = new ParentsRepository(this.tx);
    const enrollmentsRepository = new StudentEnrollmentsRepository(this.tx);
    const languagesRepository = new StudentLanguagesRepository(this.tx);
    const caseRepository = new CaseRepository(this.tx);

    for (let i = 0; i < totalStudents; i += BATCH_SIZE) {
      const batch = input.students.slice(
        i,
        Math.min(i + BATCH_SIZE, totalStudents)
      );

      try {
        // biome-ignore lint/nursery/noAwaitInLoop: Fix later
        await this.validateBatchData(batch);

        // Prepare student data for insertion
        const studentsToInsert = batch.map((studentData) => ({
          studentIdNumber: studentData.studentIdNumber,
          firstName: studentData.firstName,
          lastName: studentData.lastName,
          preferredName: studentData.preferredName,
          dateOfBirth: studentData.dateOfBirth.toISOString().split('T')[0],
          gender: studentData.gender,
          grade: studentData.grade,
          primarySchoolId: studentData.primarySchoolId,
          privateSchool: studentData.outOfDistrict
            ? studentData.privateSchool
            : null,
        }));

        // Create students
        const insertedStudents =
          await studentRepository.createStudents(studentsToInsert);

        // Create a map for quick lookup
        const studentIdMap = new Map(
          insertedStudents.map((student: { id: string }, index: number) => [
            batch[index].id,
            student.id,
          ])
        );

        // Prepare batch data for related tables
        const enrollments: NewStudentEnrollment[] = [];
        const languages: NewStudentLanguage[] = [];
        const parentRelationships: NewStudentParent[] = [];
        const documentsToUpload: Array<{
          studentId: string;
          tempId: string;
          doc: DocumentInput;
        }> = [];

        // Collect all parents that need to be checked/created
        const allParentsData = new Map<string, ParentInput>();

        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;

          // Prepare enrollment
          enrollments.push({
            studentId,
            schoolId: studentData.primarySchoolId,
          });

          // Prepare languages
          for (const languageId of studentData.languageIds) {
            languages.push({
              studentId,
              languageId,
              isPrimary: languageId === studentData.primaryLanguageId,
            });
          }

          // Collect unique parents
          for (const parent of studentData.parents) {
            const parentKey = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
            allParentsData.set(parentKey, parent);
          }

          // Collect documents for later upload
          for (const doc of studentData.documents || []) {
            documentsToUpload.push({
              studentId,
              tempId: studentData.id,
              doc,
            });
          }
        }

        // Process parents
        const existingParentMap = await this.processParents(
          parentsRepository,
          allParentsData
        );

        // Create student-parent relationships
        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;
          if (!studentId) {
            continue;
          }

          for (const parent of studentData.parents) {
            const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
            const parentId = existingParentMap.get(key);

            if (parentId) {
              parentRelationships.push({
                studentId,
                parentId,
                isPrimaryContact: parent.isPrimaryContact,
                hasPickupAuthorization: parent.hasPickupAuthorization,
              });
            }
          }
        }

        // Batch insert all relationships using repositories sequentially to avoid transaction conflicts
        try {
          await Promise.all([
            enrollmentsRepository.createEnrollments(enrollments),
            languagesRepository.createStudentLanguages(languages),
            parentsRepository.createStudentParentRelationships(
              parentRelationships
            ),
          ]);
        } catch (error) {
          logger.error({error}, '❌ ERROR CREATING STUDENT RELATIONSHIPS');
          throw new Error('Failed to create student relationships. Please check your data and try again.');
        }

        // Create cases for all students in batch
        const casesToCreate: NewCase[] = [];
        const now = new Date();
        const futureDate = new Date(now);
        futureDate.setFullYear(futureDate.getFullYear() + 1); // Set IEP end date to 1 year from now

        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;
          if (studentId) {
            const isCaseActive =
              studentData.documents?.some(
                (doc) => doc.category === DocumentCategoryEnum.BACKGROUND
              ) ?? false;

            casesToCreate.push({
              studentId,
              status: CaseStatusEnum.READY_FOR_EVALUATION,
              priority: CasePriorityEnum.MEDIUM, // Default priority as requested
              caseType: CaseTypeEnum.INITIAL_EVALUATION, // Default type as requested
              dateOfConsent: studentData.dateOfConsent,
              isActive: isCaseActive,
              iepStatus: IepStatusEnum.INACTIVE, // Start as inactive
              iepStartDate: now,
              iepEndDate: futureDate,
            });
          }
        }

        // Create cases sequentially to avoid transaction conflicts
        if (casesToCreate.length > 0) {
          try {
            await Promise.all(
              casesToCreate.map((caseData) => caseRepository.createCase(caseData))
            );
          } catch (error) {
            logger.error({error}, '❌ ERROR CREATING CASES');
            throw new Error('Failed to create cases for students. Please try again.');
          }
        }

        // Handle document uploads asynchronously (outside transaction)
        this.handleDocumentUploads(documentsToUpload, user.id);

        // Record success for all students in batch
        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;
          if (studentId) {
            results.push({
              tempId: studentData.id,
              success: true,
              studentId,
            });
          }
        }
      } catch (error) {
        const errorMessage = this.parseDbError(error);

        // Record failure for entire batch
        for (const studentData of batch) {
          results.push({
            tempId: studentData.id,
            success: false,
            error: errorMessage,
          });
        }
      }
    }

    return results;
  }

  private async validateBatchData(batch: BulkStudentInput[]): Promise<void> {
    // Check for duplicate student ID numbers within the batch
    const studentIds = batch.map((s) => s.studentIdNumber);

    const duplicateIds = studentIds.filter(
      (id, index) => studentIds.indexOf(id) !== index
    );

    if (duplicateIds.length > 0) {
      throw new Error(
        `Duplicate student ID numbers in batch: ${duplicateIds.join(', ')}`
      );
    }

    // Validate required fields
    for (const student of batch) {
      if (!student.studentIdNumber?.trim()) {
        throw new Error(
          `Student ID number is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (!student.firstName?.trim()) {
        throw new Error(
          `First name is required for student ID: ${student.studentIdNumber}`
        );
      }
      if (!student.lastName?.trim()) {
        throw new Error(
          `Last name is required for student ID: ${student.studentIdNumber}`
        );
      }
      if (!student.primarySchoolId?.trim()) {
        throw new Error(
          `School is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (!student.dateOfConsent) {
        throw new Error(
          `Date of consent is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (!student.languageIds || student.languageIds.length === 0) {
        throw new Error(
          `At least one language is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (student.parents.length === 0) {
        throw new Error(
          `At least one parent/guardian is required for student: ${student.firstName} ${student.lastName}`
        );
      }

      // Validate out-of-district and private school relationship
      if (student.outOfDistrict && !student.privateSchool?.trim()) {
        throw new Error(
          `Private school name is required when student is out of district: ${student.firstName} ${student.lastName}`
        );
      }
    }

    // Check for existing students in database
    await this.checkForExistingStudents(batch);
  }

  private async checkForExistingStudents(batch: StudentInput[]): Promise<void> {
    const studentRepository = new StudentRepository(this.tx);

    try {
      for (const student of batch) {
        // biome-ignore lint/nursery/noAwaitInLoop: Fix later
        const existing = await studentRepository.getStudentByStudentIdNumber(
          student.studentIdNumber
        );

        if (existing) {
          throw new Error(
            `Student with ID "${student.studentIdNumber}" already exists in the system. Please use a different Student ID.`
          );
        }
      }
    } catch (error) {
      // Re-throw the error with better context if it's a database error
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          throw error; // Re-throw our custom error
        }
        // Handle database connection or other errors
        throw new Error(`Failed to validate student data: ${error.message}`);
      }
      throw new Error('Failed to validate student data due to an unexpected error.');
    }
  }

  private parseDbError(error: unknown): string {
    if (!error || typeof error !== 'object') {
      return 'Unknown error occurred';
    }

    // biome-ignore lint/suspicious/noExplicitAny: Supabase error
    const dbError = error as any;

    // Handle transaction abort errors
    if (dbError.message?.includes('current transaction is aborted')) {
      return 'Database transaction was aborted due to an error. Please check your data and try again.';
    }

    // Handle PostgreSQL constraint violations
    if (dbError.code === '23505') {
      // Unique constraint violation
      if (dbError.constraint === 'student_id_school_unique') {
        return 'A student with this Student ID is already enrolled in the selected school. Please check the Student ID and try again.';
      }
      if (dbError.constraint === 'students_student_id_number_unique') {
        return 'A student with this Student ID already exists. Please use a different Student ID.';
      }
      return 'Duplicate data detected. Please check your entries and try again.';
    }

    if (dbError.code === '23503') {
      // Foreign key constraint violation
      return 'Invalid reference data. Please ensure all schools and languages exist.';
    }

    if (dbError.code === '23502') {
      // Not null constraint violation
      return 'Required field is missing. Please check all required fields are filled.';
    }

    // Handle other database errors
    if (dbError.code?.startsWith('23')) {
      return 'Data validation error. Please check your entries and try again.';
    }

    // Handle connection and timeout errors
    if (dbError.code === '08000' || dbError.code === '08003' || dbError.code === '08006') {
      return 'Database connection error. Please try again in a moment.';
    }

    // Handle general errors
    if (error instanceof Error) {
      return error.message;
    }

    return 'An unexpected error occurred while saving students.';
  }

  private async processParents(
    parentsRepository: ParentsRepository,
    allParentsData: Map<string, ParentInput>
  ): Promise<Map<string, string>> {
    const parentValues = Array.from(allParentsData.values());

    // Prepare search keys for existing parents
    const searchKeys = parentValues.map((parent) => ({
      firstName: parent.firstName,
      lastName: parent.lastName,
      identifier: parent.primaryEmail || parent.primaryPhone || '',
    }));

    // Find existing parents
    const existingParents =
      await parentsRepository.findExistingParents(searchKeys);

    // Create map of existing parents
    const existingParentMap = new Map(
      existingParents.map((p: Parent) => {
        const key = `${p.firstName}-${p.lastName}-${p.primaryEmail || p.primaryPhone}`;
        return [key, p.id];
      })
    );

    // Prepare new parents for batch insert
    const newParents: CreateParentData[] = parentValues
      .filter((parent) => {
        const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
        return !existingParentMap.has(key);
      })
      .map((parent) => ({
        firstName: parent.firstName,
        middleName: parent.middleName || null,
        lastName: parent.lastName,
        primaryEmail: parent.primaryEmail || null,
        secondaryEmail: parent.secondaryEmail || null,
        primaryPhone: parent.primaryPhone || null,
        secondaryPhone: parent.secondaryPhone || null,
        relationshipType: parent.relationshipType,
      }));

    // Batch insert new parents
    let insertedParents: Array<{ id: string }> = [];

    if (newParents.length > 0) {
      insertedParents = await parentsRepository.createParents(newParents);
    }

    // Update parent map with newly inserted parents
    let insertIndex = 0;
    for (const parent of parentValues) {
      const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
      if (!existingParentMap.has(key) && insertIndex < insertedParents.length) {
        existingParentMap.set(key, insertedParents[insertIndex].id);
        insertIndex++;
      }
    }

    return existingParentMap;
  }

  private handleDocumentUploads(
    documentsToUpload: Array<{
      studentId: string;
      tempId: string;
      doc: DocumentInput;
    }>,
    userId: string
  ): void {
    // Handle document uploads asynchronously (non-blocking)
    // Note: This will be processed outside the transaction context
    if (documentsToUpload.length === 0) {
      return;
    }

    // Process document uploads in the background
    setImmediate(async () => {
      const { createDatabaseClient } = await import('@lilypad/db/client');

      for (const { studentId, doc } of documentsToUpload) {
        try {
          // Convert serializable file back to File if needed
          const file = isSerializableFile(doc.file)
            ? serializableToFile(doc.file)
            : doc.file;

          // Upload file to storage using server-side function
          // biome-ignore lint/nursery/noAwaitInLoop: Fix later
          const uploadResult = await uploadStudentDocumentServer({
            file,
            studentId,
            userId,
          });

          // Create new database connection for this operation
          const dbAdmin = await createDatabaseClient({ admin: true });

          const documentsRepository = new DocumentsRepository(
            await dbAdmin.transaction(async (tx) => tx) // TODO: This is a hack to get the transaction to work
          );

          // Insert document record
          await documentsRepository.createDocument({
            studentId,
            name: file.name,
            url: uploadResult.url,
            category: doc.category || DocumentCategoryEnum.BACKGROUND,
            uploadedUserId: userId,
          });
        } catch (error) {
          console.error('Document upload error:', error);
        }
      }
    });
  }
}
