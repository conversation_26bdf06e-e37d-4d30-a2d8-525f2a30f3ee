import type {
  CaseP<PERSON>rityEnum,
  CaseTypeEnum,
  GenderEnum,
  ParentRelationshipEnum,
  SchoolGradeEnum,
} from '@lilypad/db/enums';
import type { DocumentCategoryEnum } from '@lilypad/db/schema/enums';

export type SerializableFile = {
  name: string;
  size: number;
  type: string;
  data: string; // Base64 encoded
};

export interface ParentInput {
  firstName: string;
  middleName?: string;
  lastName: string;
  relationshipType: ParentRelationshipEnum;
  primaryEmail?: string;
  secondaryEmail?: string;
  primaryPhone?: string;
  secondaryPhone?: string;
  isPrimaryContact: boolean;
  hasPickupAuthorization: boolean;
}

export interface DocumentInput {
  file: File | SerializableFile;
  category?: DocumentCategoryEnum;
}

export interface CaseCreationInput {
  caseType?: CaseTypeEnum;
  priority?: CasePriorityEnum;
  dateOfConsent: Date;
  initialNotes?: string;
}

export interface NotesInput {
  content?: string;
}

export function isSerializableFile(
  file: File | SerializableFile
): file is SerializableFile {
  return (
    typeof file === 'object' && 'data' in file && typeof file.data === 'string'
  );
}

export function isFileObject(file: File | SerializableFile): file is File {
  return file instanceof File;
}

export interface StudentInput {
  id: string;
  studentIdNumber: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  preferredName?: string;
  dateOfBirth: Date;
  grade: SchoolGradeEnum;
  gender: GenderEnum;
  primarySchoolId: string;
  privateSchool?: string;
  outOfDistrict?: boolean;
  languageIds: string[];
  primaryLanguageId: string | null;
  parents: ParentInput[];
  documents?: DocumentInput[];
}

// Extended student input for bulk creation with dateOfConsent
export interface BulkStudentInput extends StudentInput {
  dateOfConsent: Date;
}

// Extended student input for single student creation with case and notes
export interface AddStudentInput extends StudentInput {
  caseInfo: CaseCreationInput;
  notes?: NotesInput;
}

export interface BulkCreateStudentsInput {
  students: BulkStudentInput[];
}

export interface StudentSaveResult {
  tempId: string;
  success: boolean;
  studentId?: string;
  error?: string;
}

// Single student creation result with case information
export interface AddStudentResult {
  success: boolean;
  studentId?: string;
  caseId?: string;
  error?: string;
}
