import { WorkflowStepsRepository } from '@lilypad/db/repository/workflow-steps';
import { WorkflowsRepository } from '@lilypad/db/repository/workflows';
import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import {
  canCompleteStepInputSchema,
  completeWorkflowStepInputSchema,
  getWorkflowStepTasksInputSchema,
} from '../schemas/workflows';

export const workflowsRouter = createTRPCRouter({
  getWorkflowStepTasks: authenticatedProcedure
    .input(getWorkflowStepTasksInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new WorkflowStepsRepository(tx);
        return await repository.getWorkflowStepTasks(
          input.workflowStepId,
          input.caseId
        );
      });
    }),

  canCompleteStepBasedOnTasks: authenticatedProcedure
    .input(canCompleteStepInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new WorkflowsRepository(tx);
        return await repository.canCompleteStepBasedOnTasks(
          input.caseId,
          input.stepId
        );
      });
    }),

  completeWorkflowStep: authenticatedProcedure
    .input(completeWorkflowStepInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new WorkflowsRepository(tx);
        return await repository.completeWorkflowStep(
          input.caseId,
          input.stepId
        );
      });
    }),
});
