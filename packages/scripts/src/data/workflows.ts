import { RoleEnum, TaskTypeEnum } from '@lilypad/db/enums';

export const IEP_WORKFLOW = {
  name: 'IEP Evaluation',
  description:
    'Complete process for Initial IEP Evaluation including referral, assessment, and plan development',
  version: '1.0',
  isActive: true,
} as const;

export const IEP_WORKFLOW_STEPS = [
  {
    stepNumber: 1,
    name: 'Initial Referral',
    description:
      'Receive and review initial referral for special education evaluation',
    estimatedDays: 3,
    isOptional: false,
  },
  {
    stepNumber: 2,
    name: 'Team Meeting',
    description:
      'Conduct initial team meeting to discuss referral and determine next steps',
    estimatedDays: 5,
    isOptional: false,
  },
  {
    stepNumber: 3,
    name: 'Create Evaluation Plan',
    description:
      'Develop comprehensive evaluation plan and obtain parental consent',
    estimatedDays: 7,
    isOptional: false,
  },
  {
    stepNumber: 4,
    name: 'Conduct Assessments',
    description:
      'Administer psychological, educational, and other required assessments',
    estimatedDays: 21,
    isOptional: false,
  },
  {
    stepNumber: 5,
    name: 'Analyze Results',
    description: 'Analyze assessment results and prepare evaluation report',
    estimatedDays: 10,
    isOptional: false,
  },
  {
    stepNumber: 6,
    name: 'Make Eligibility Decision',
    description: 'Team meeting to determine special education eligibility',
    estimatedDays: 3,
    isOptional: false,
  },
  {
    stepNumber: 7,
    name: 'Develop IEP',
    description:
      'Create Individualized Education Program if student is eligible',
    estimatedDays: 7,
    isOptional: false,
  },
  {
    stepNumber: 8,
    name: 'Implement Services',
    description: 'Begin implementation of IEP services and accommodations',
    estimatedDays: 5,
    isOptional: false,
  },
  {
    stepNumber: 9,
    name: 'Monitor Progress',
    description: 'Monitor student progress and adjust services as needed',
    estimatedDays: 30,
    isOptional: false,
  },
  {
    stepNumber: 10,
    name: 'Annual Review',
    description: 'Conduct annual IEP review and update plan as necessary',
    estimatedDays: 5,
    isOptional: false,
  },
] as const;

export const IEP_WORKFLOW_STEP_TASKS = [
  // Step 1: Initial Referral
  {
    stepNumber: 1,
    tasks: [
      {
        taskType: TaskTypeEnum.COMPLETE_REFERRAL_FORM,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: '15 min',
        autoAssignRole: RoleEnum.SCHOOL_COORDINATOR,
      },
    ],
  },
  // Step 2: Team Meeting
  {
    stepNumber: 2,
    tasks: [
      {
        taskType: TaskTypeEnum.SCHEDULE_STUDENT_EVALUATIONS,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: '15 min',
        autoAssignRole: RoleEnum.SCHOOL_COORDINATOR,
      },
      {
        taskType: TaskTypeEnum.GENERATE_CALENDAR_INVITES,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: null,
        autoAssignRole: RoleEnum.ASSISTANT,
      },
    ],
  },
  // Step 3: Create Evaluation Plan
  {
    stepNumber: 3,
    tasks: [
      {
        taskType: TaskTypeEnum.CREATE_EVALUATION_PLAN,
        isRequired: false,
        orderIndex: 1,
        estimatedTimeline: '30 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.PREPARE_RATING_SCALES,
        isRequired: true,
        orderIndex: 2,
        estimatedTimeline: '45 min',
        autoAssignRole: RoleEnum.ASSISTANT,
      },
      {
        taskType: TaskTypeEnum.REVIEW_AND_SEND_RATING_SCALES,
        isRequired: true,
        orderIndex: 3,
        estimatedTimeline: '30 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.MONITOR_RATING_SCALES,
        isRequired: false,
        orderIndex: 4,
        estimatedTimeline: null,
        autoAssignRole: RoleEnum.ASSISTANT,
      },
      {
        taskType: TaskTypeEnum.PREPARE_ASSESSMENT_MATERIALS,
        isRequired: false,
        orderIndex: 5,
        estimatedTimeline: null,
        autoAssignRole: RoleEnum.ASSISTANT,
      },
    ],
  },
  // Step 4: Conduct Assessments
  {
    stepNumber: 4,
    tasks: [
      {
        taskType: TaskTypeEnum.PREPARE_FOR_EVALUATION,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: '30 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.JOIN_EVALUATION_AS_PSYCHOLOGIST,
        isRequired: true,
        orderIndex: 2,
        estimatedTimeline: '5 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.JOIN_EVALUATION_AS_PROCTOR,
        isRequired: true,
        orderIndex: 3,
        estimatedTimeline: '5 min',
        autoAssignRole: RoleEnum.PROCTOR,
      },
      {
        taskType: TaskTypeEnum.MARK_EVALUATION_COMPLETE,
        isRequired: true,
        orderIndex: 4,
        estimatedTimeline: '1 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.COMPLETE_STUDENT_INTERVIEW,
        isRequired: true,
        orderIndex: 5,
        estimatedTimeline: '15 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.UPLOAD_PROTOCOLS,
        isRequired: true,
        orderIndex: 6,
        estimatedTimeline: '15 min',
        autoAssignRole: RoleEnum.SCHOOL_ADMIN,
      },
      {
        taskType: TaskTypeEnum.UPDATE_ASSESSMENT_SCORES,
        isRequired: true,
        orderIndex: 7,
        estimatedTimeline: '30 min',
        autoAssignRole: RoleEnum.ASSISTANT,
      },
    ],
  },
  // Step 5: Analyze Results
  {
    stepNumber: 5,
    tasks: [
      {
        taskType: TaskTypeEnum.GENERATE_REPORT_DRAFT,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: '2 hours',
        autoAssignRole: RoleEnum.ASSISTANT,
      },
      {
        taskType: TaskTypeEnum.FINALIZE_EVALUATION_REPORT,
        isRequired: true,
        orderIndex: 2,
        estimatedTimeline: '3 hours',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.SCORE_REPORT_QUALITY,
        isRequired: false,
        orderIndex: 3,
        estimatedTimeline: null,
        autoAssignRole: RoleEnum.ASSISTANT,
      },
      {
        taskType: TaskTypeEnum.REVIEW_FINAL_REPORT,
        isRequired: true,
        orderIndex: 4,
        estimatedTimeline: '45 min',
        autoAssignRole: RoleEnum.CLINICAL_DIRECTOR,
      },
      {
        taskType: TaskTypeEnum.MARK_REPORT_RECEIVED,
        isRequired: true,
        orderIndex: 5,
        estimatedTimeline: '20 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
    ],
  },
  // Step 6: Make Eligibility Decision
  {
    stepNumber: 6,
    tasks: [
      {
        taskType: TaskTypeEnum.SCHEDULE_IEP_MEETING,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: '15 min',
        autoAssignRole: RoleEnum.SCHOOL_COORDINATOR,
      },
      {
        taskType: TaskTypeEnum.PREPARE_FOR_IEP_MEETING,
        isRequired: true,
        orderIndex: 2,
        estimatedTimeline: '15 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
      {
        taskType: TaskTypeEnum.SEND_MEETING_INVITATIONS,
        isRequired: true,
        orderIndex: 3,
        estimatedTimeline: null,
        autoAssignRole: RoleEnum.ASSISTANT,
      },
    ],
  },
  // Step 7: Develop IEP
  {
    stepNumber: 7,
    tasks: [
      {
        taskType: TaskTypeEnum.COMPLETE_IEP_MEETING,
        isRequired: true,
        orderIndex: 1,
        estimatedTimeline: '1 min',
        autoAssignRole: RoleEnum.PSYCHOLOGIST,
      },
    ],
  },
  // Steps 8-10: Placeholder steps (no tasks for now)
  {
    stepNumber: 8,
    tasks: [],
  },
  {
    stepNumber: 9,
    tasks: [],
  },
  {
    stepNumber: 10,
    tasks: [],
  },
] as const;
