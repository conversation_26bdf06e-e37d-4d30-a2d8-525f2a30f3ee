{"editor.defaultFormatter": "biomejs.biome", "[javascript][typescript][javascriptreact][typescriptreact][json][jsonc][graphql][markdown]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"quickfix.biome": "never", "source.organizeImports.biome": "never"}, "editor.formatOnSave": true, "editor.formatOnPaste": false, "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "terminal.integrated.localEchoStyle": "dim", "search.exclude": {"**/node_modules": true, "**/.next": true}, "typescript.preferences.autoImportSpecifierExcludeRegexes": ["@radix-ui", "next/dist", "next/document", "next/head", "next/router"], "todohighlight.keywords": [{"text": "TODO:", "color": "#000000", "backgroundColor": "#FFD700", "overviewRulerColor": "grey"}, {"text": "NOTE:", "color": "#000000", "backgroundColor": "#39BDF8", "overviewRulerColor": "grey"}, {"text": "DONE:", "color": "#000000", "backgroundColor": "#06DF72", "overviewRulerColor": "grey"}]}