{"id": "87a1277e-0ce6-40d3-ab93-d0df674b8cc1", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.assessment_sessions": {"name": "assessment_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "psychologist_id": {"name": "psychologist_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_date": {"name": "session_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "session_duration": {"name": "session_duration", "type": "integer", "primaryKey": false, "notNull": false}, "session_type": {"name": "session_type", "type": "session_type", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "referral_reason": {"name": "referral_reason", "type": "text", "primaryKey": false, "notNull": false}, "background_information": {"name": "background_information", "type": "text", "primaryKey": false, "notNull": false}, "behavioral_observations": {"name": "behavioral_observations", "type": "text", "primaryKey": false, "notNull": false}, "testing_conditions": {"name": "testing_conditions", "type": "text", "primaryKey": false, "notNull": false}, "accommodations_provided": {"name": "accommodations_provided", "type": "jsonb", "primaryKey": false, "notNull": false}, "environmental_factors": {"name": "environmental_factors", "type": "text", "primaryKey": false, "notNull": false}, "session_status": {"name": "session_status", "type": "session_status", "primaryKey": false, "notNull": false, "default": "'SCHEDULED'"}, "validity_concerns": {"name": "validity_concerns", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"session_student_idx": {"name": "session_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "session_psychologist_idx": {"name": "session_psychologist_idx", "columns": [{"expression": "psychologist_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "session_case_idx": {"name": "session_case_idx", "columns": [{"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "session_date_idx": {"name": "session_date_idx", "columns": [{"expression": "session_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "session_status_idx": {"name": "session_status_idx", "columns": [{"expression": "session_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "session_type_idx": {"name": "session_type_idx", "columns": [{"expression": "session_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"assessment_sessions_student_id_students_id_fk": {"name": "assessment_sessions_student_id_students_id_fk", "tableFrom": "assessment_sessions", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assessment_sessions_case_id_cases_id_fk": {"name": "assessment_sessions_case_id_cases_id_fk", "tableFrom": "assessment_sessions", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assessment_sessions_psychologist_id_users_id_fk": {"name": "assessment_sessions_psychologist_id_users_id_fk", "tableFrom": "assessment_sessions", "tableTo": "users", "columnsFrom": ["psychologist_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_assessment_sessions_based_on_role": {"name": "users_can_view_assessment_sessions_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')\n    )\n  \n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = assessment_sessions.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = assessment_sessions.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = assessment_sessions.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t)\n\t\t"}, "authorized_roles_can_create_assessment_sessions": {"name": "authorized_roles_can_create_assessment_sessions", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')\n    )\n  \n\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = assessment_sessions.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = assessment_sessions.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = assessment_sessions.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\tAND assessment_sessions.psychologist_id = auth.uid()\n\t\t"}, "authorized_roles_can_update_assessment_sessions": {"name": "authorized_roles_can_update_assessment_sessions", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')\n    )\n  \n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = assessment_sessions.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = assessment_sessions.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = assessment_sessions.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t)\n\t\t"}, "only_superuser_can_delete_assessment_sessions": {"name": "only_superuser_can_delete_assessment_sessions", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.index_scores": {"name": "index_scores", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "administration_id": {"name": "administration_id", "type": "uuid", "primaryKey": false, "notNull": true}, "index_id": {"name": "index_id", "type": "uuid", "primaryKey": false, "notNull": true}, "composite_score": {"name": "composite_score", "type": "integer", "primaryKey": false, "notNull": false}, "percentile_rank": {"name": "percentile_rank", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "confidence_interval_lower": {"name": "confidence_interval_lower", "type": "integer", "primaryKey": false, "notNull": false}, "confidence_interval_upper": {"name": "confidence_interval_upper", "type": "integer", "primaryKey": false, "notNull": false}, "confidence_level": {"name": "confidence_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 95}, "descriptive_category": {"name": "descriptive_category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "qualitative_descriptor": {"name": "qualitative_descriptor", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "calculation_method": {"name": "calculation_method", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "component_subtests": {"name": "component_subtests", "type": "jsonb", "primaryKey": false, "notNull": false}, "interpretation_notes": {"name": "interpretation_notes", "type": "text", "primaryKey": false, "notNull": false}, "strengths_identified": {"name": "strengths_identified", "type": "text", "primaryKey": false, "notNull": false}, "weaknesses_identified": {"name": "weaknesses_identified", "type": "text", "primaryKey": false, "notNull": false}, "is_valid": {"name": "is_valid", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "validity_notes": {"name": "validity_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"index_score_admin_index_unique": {"name": "index_score_admin_index_unique", "columns": [{"expression": "administration_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "index_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "index_score_admin_idx": {"name": "index_score_admin_idx", "columns": [{"expression": "administration_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_score_index_idx": {"name": "index_score_index_idx", "columns": [{"expression": "index_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_score_composite_idx": {"name": "index_score_composite_idx", "columns": [{"expression": "composite_score", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_score_percentile_idx": {"name": "index_score_percentile_idx", "columns": [{"expression": "percentile_rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_score_valid_idx": {"name": "index_score_valid_idx", "columns": [{"expression": "is_valid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"index_scores_administration_id_test_administrations_id_fk": {"name": "index_scores_administration_id_test_administrations_id_fk", "tableFrom": "index_scores", "tableTo": "test_administrations", "columnsFrom": ["administration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "index_scores_index_id_test_indices_id_fk": {"name": "index_scores_index_id_test_indices_id_fk", "tableFrom": "index_scores", "tableTo": "test_indices", "columnsFrom": ["index_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_index_scores_based_on_role": {"name": "users_can_view_index_scores_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM test_administrations ta\n\t\t\t\t\tJOIN assessment_sessions a ON a.id = ta.session_id\n\t\t\t\t\tWHERE ta.id = index_scores.administration_id\n\t\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = a.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = a.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = a.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t\t)\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.index_subtest_mappings": {"name": "index_subtest_mappings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "index_id": {"name": "index_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subtest_id": {"name": "subtest_id", "type": "uuid", "primaryKey": false, "notNull": true}, "weight": {"name": "weight", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'1.00'"}, "is_required": {"name": "is_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"mapping_index_subtest_unique": {"name": "mapping_index_subtest_unique", "columns": [{"expression": "index_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "subtest_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "mapping_index_idx": {"name": "mapping_index_idx", "columns": [{"expression": "index_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "mapping_subtest_idx": {"name": "mapping_subtest_idx", "columns": [{"expression": "subtest_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"index_subtest_mappings_index_id_test_indices_id_fk": {"name": "index_subtest_mappings_index_id_test_indices_id_fk", "tableFrom": "index_subtest_mappings", "tableTo": "test_indices", "columnsFrom": ["index_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "index_subtest_mappings_subtest_id_subtests_id_fk": {"name": "index_subtest_mappings_subtest_id_subtests_id_fk", "tableFrom": "index_subtest_mappings", "tableTo": "subtests", "columnsFrom": ["subtest_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_index_subtest_mappings": {"name": "all_users_can_view_index_subtest_mappings", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subtest_scores": {"name": "subtest_scores", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "administration_id": {"name": "administration_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subtest_id": {"name": "subtest_id", "type": "uuid", "primaryKey": false, "notNull": true}, "raw_score": {"name": "raw_score", "type": "integer", "primaryKey": false, "notNull": false}, "scaled_score": {"name": "scaled_score", "type": "integer", "primaryKey": false, "notNull": false}, "percentile_rank": {"name": "percentile_rank", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "confidence_interval_lower": {"name": "confidence_interval_lower", "type": "integer", "primaryKey": false, "notNull": false}, "confidence_interval_upper": {"name": "confidence_interval_upper", "type": "integer", "primaryKey": false, "notNull": false}, "confidence_level": {"name": "confidence_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 95}, "age_equivalent": {"name": "age_equivalent", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "grade_equivalent": {"name": "grade_equivalent", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "completion_time_minutes": {"name": "completion_time_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "discontinued_item": {"name": "discontinued_item", "type": "integer", "primaryKey": false, "notNull": false}, "descriptive_category": {"name": "descriptive_category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "qualitative_descriptor": {"name": "qualitative_descriptor", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "strengths_identified": {"name": "strengths_identified", "type": "text", "primaryKey": false, "notNull": false}, "weaknesses_identified": {"name": "weaknesses_identified", "type": "text", "primaryKey": false, "notNull": false}, "scoring_notes": {"name": "scoring_notes", "type": "text", "primaryKey": false, "notNull": false}, "is_valid": {"name": "is_valid", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "validity_notes": {"name": "validity_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"score_admin_subtest_unique": {"name": "score_admin_subtest_unique", "columns": [{"expression": "administration_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "subtest_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "score_admin_idx": {"name": "score_admin_idx", "columns": [{"expression": "administration_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "score_subtest_idx": {"name": "score_subtest_idx", "columns": [{"expression": "subtest_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "score_scaled_idx": {"name": "score_scaled_idx", "columns": [{"expression": "scaled_score", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "score_percentile_idx": {"name": "score_percentile_idx", "columns": [{"expression": "percentile_rank", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "score_valid_idx": {"name": "score_valid_idx", "columns": [{"expression": "is_valid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subtest_scores_administration_id_test_administrations_id_fk": {"name": "subtest_scores_administration_id_test_administrations_id_fk", "tableFrom": "subtest_scores", "tableTo": "test_administrations", "columnsFrom": ["administration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subtest_scores_subtest_id_subtests_id_fk": {"name": "subtest_scores_subtest_id_subtests_id_fk", "tableFrom": "subtest_scores", "tableTo": "subtests", "columnsFrom": ["subtest_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_subtest_scores_based_on_role": {"name": "users_can_view_subtest_scores_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM test_administrations ta\n\t\t\t\t\tJOIN assessment_sessions a ON a.id = ta.session_id\n\t\t\t\t\tWHERE ta.id = subtest_scores.administration_id\n\t\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = a.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = a.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = a.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t\t)\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subtests": {"name": "subtests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "battery_id": {"name": "battery_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "subtest_type": {"name": "subtest_type", "type": "subtest_type", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "measured_abilities": {"name": "measured_abilities", "type": "jsonb", "primaryKey": false, "notNull": false}, "time_limit_minutes": {"name": "time_limit_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "score_range_min": {"name": "score_range_min", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "score_range_max": {"name": "score_range_max", "type": "integer", "primaryKey": false, "notNull": false, "default": 19}, "mean_score": {"name": "mean_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 10}, "standard_deviation": {"name": "standard_deviation", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "administration_instructions": {"name": "administration_instructions", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subtest_battery_code_unique": {"name": "subtest_battery_code_unique", "columns": [{"expression": "battery_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "subtest_battery_idx": {"name": "subtest_battery_idx", "columns": [{"expression": "battery_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subtest_type_idx": {"name": "subtest_type_idx", "columns": [{"expression": "subtest_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subtest_sort_idx": {"name": "subtest_sort_idx", "columns": [{"expression": "sort_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subtest_active_idx": {"name": "subtest_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subtests_battery_id_test_batteries_id_fk": {"name": "subtests_battery_id_test_batteries_id_fk", "tableFrom": "subtests", "tableTo": "test_batteries", "columnsFrom": ["battery_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_subtests": {"name": "all_users_can_view_subtests", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_administrations": {"name": "test_administrations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "battery_id": {"name": "battery_id", "type": "uuid", "primaryKey": false, "notNull": true}, "administration_order": {"name": "administration_order", "type": "integer", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "administration_notes": {"name": "administration_notes", "type": "text", "primaryKey": false, "notNull": false}, "accommodations_used": {"name": "accommodations_used", "type": "jsonb", "primaryKey": false, "notNull": false}, "discontinuation_notes": {"name": "discontinuation_notes", "type": "text", "primaryKey": false, "notNull": false}, "validity_indicators": {"name": "validity_indicators", "type": "jsonb", "primaryKey": false, "notNull": false}, "admin_status": {"name": "admin_status", "type": "admin_status", "primaryKey": false, "notNull": false, "default": "'PLANNED'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"admin_session_idx": {"name": "admin_session_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_battery_idx": {"name": "admin_battery_idx", "columns": [{"expression": "battery_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_order_idx": {"name": "admin_order_idx", "columns": [{"expression": "administration_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_status_idx": {"name": "admin_status_idx", "columns": [{"expression": "admin_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_start_time_idx": {"name": "admin_start_time_idx", "columns": [{"expression": "start_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"test_administrations_session_id_assessment_sessions_id_fk": {"name": "test_administrations_session_id_assessment_sessions_id_fk", "tableFrom": "test_administrations", "tableTo": "assessment_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_administrations_battery_id_test_batteries_id_fk": {"name": "test_administrations_battery_id_test_batteries_id_fk", "tableFrom": "test_administrations", "tableTo": "test_batteries", "columnsFrom": ["battery_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_test_administrations_based_on_role": {"name": "users_can_view_test_administrations_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR')\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM assessment_sessions a\n\t\t\t\t\tWHERE a.id = test_administrations.session_id\n\t\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = a.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = a.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = a.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t\t)\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_batteries": {"name": "test_batteries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "category": {"name": "category", "type": "test_category", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "publisher": {"name": "publisher", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "age_range_min": {"name": "age_range_min", "type": "integer", "primaryKey": false, "notNull": false}, "age_range_max": {"name": "age_range_max", "type": "integer", "primaryKey": false, "notNull": false}, "administration_time": {"name": "administration_time", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "norming_information": {"name": "norming_information", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"battery_category_idx": {"name": "battery_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "battery_age_range_idx": {"name": "battery_age_range_idx", "columns": [{"expression": "age_range_min", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "age_range_max", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "battery_active_idx": {"name": "battery_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "battery_code_idx": {"name": "battery_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"test_batteries_code_unique": {"name": "test_batteries_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {"all_users_can_view_test_batteries": {"name": "all_users_can_view_test_batteries", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_indices": {"name": "test_indices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "battery_id": {"name": "battery_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "index_type": {"name": "index_type", "type": "index_type", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "score_range_min": {"name": "score_range_min", "type": "integer", "primaryKey": false, "notNull": false, "default": 40}, "score_range_max": {"name": "score_range_max", "type": "integer", "primaryKey": false, "notNull": false, "default": 160}, "mean_score": {"name": "mean_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "standard_deviation": {"name": "standard_deviation", "type": "integer", "primaryKey": false, "notNull": false, "default": 15}, "interpretive_guidelines": {"name": "interpretive_guidelines", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"index_battery_code_unique": {"name": "index_battery_code_unique", "columns": [{"expression": "battery_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "index_battery_idx": {"name": "index_battery_idx", "columns": [{"expression": "battery_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_type_idx": {"name": "index_type_idx", "columns": [{"expression": "index_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_sort_idx": {"name": "index_sort_idx", "columns": [{"expression": "sort_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "index_active_idx": {"name": "index_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"test_indices_battery_id_test_batteries_id_fk": {"name": "test_indices_battery_id_test_batteries_id_fk", "tableFrom": "test_indices", "tableTo": "test_batteries", "columnsFrom": ["battery_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_test_indices": {"name": "all_users_can_view_test_indices", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "type": {"name": "type", "type": "address_type", "primaryKey": false, "notNull": true, "default": "'PHYSICAL'"}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address2": {"name": "address2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "zipcode": {"name": "zipcode", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"address_student_idx": {"name": "address_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "address_district_idx": {"name": "address_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "address_school_idx": {"name": "address_school_idx", "columns": [{"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "address_parent_idx": {"name": "address_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "address_zipcode_idx": {"name": "address_zipcode_idx", "columns": [{"expression": "zipcode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "address_type_idx": {"name": "address_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "address_student_not_null_idx": {"name": "address_student_not_null_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "student_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "address_district_not_null_idx": {"name": "address_district_not_null_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "district_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "address_school_not_null_idx": {"name": "address_school_not_null_idx", "columns": [{"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "school_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "address_parent_not_null_idx": {"name": "address_parent_not_null_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "parent_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_addresses_based_on_access": {"name": "users_can_view_addresses_based_on_access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(district_id IS NOT NULL AND \n    EXISTS (\n      SELECT 1\n      FROM user_districts\n      WHERE user_id = auth.uid()\n      AND district_id = district_id\n    )\n  )\n\t\t\tOR\n\t\t\t(school_id IS NOT NULL AND EXISTS (\n\t\t\t\tSELECT 1 FROM schools s \n\t\t\t\tWHERE s.id = school_id \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\tAND ud.district_id = s.district_id\n\t\t\t\t)\n\t\t\t))\n\t\t\tOR\n\t\t\t(student_id IS NOT NULL AND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  )\n\t\t\tOR\n\t\t\t(parent_id IS NOT NULL AND EXISTS (\n\t\t\t\tSELECT 1 FROM student_parents sp\n\t\t\t\tWHERE sp.parent_id = parent_id\n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = sp.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = sp.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = sp.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t))\n\t\t"}, "users_can_manage_addresses_for_accessible_entities": {"name": "users_can_manage_addresses_for_accessible_entities", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(district_id IS NOT NULL AND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM user_districts ud\n          WHERE ud.user_id = auth.uid()\n          AND ud.district_id = district_id\n        )\n      )\n    )\n  )\n\t\t\tOR\n\t\t\t(school_id IS NOT NULL AND EXISTS (\n\t\t\t\tSELECT 1 FROM schools s \n\t\t\t\tWHERE s.id = school_id \n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM user_districts ud\n          WHERE ud.user_id = auth.uid()\n          AND ud.district_id = s.district_id\n        )\n      )\n    )\n  \n\t\t\t))\n\t\t\tOR\n\t\t\t(student_id IS NOT NULL AND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  )\n\t\t\tOR\n\t\t\t(parent_id IS NOT NULL AND EXISTS (\n\t\t\t\tSELECT 1 FROM student_parents sp\n\t\t\t\tWHERE sp.parent_id = parent_id\n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = sp.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = sp.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = sp.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t))\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.availabilities": {"name": "availabilities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "day": {"name": "day", "type": "day_of_week", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"availability_user_idx": {"name": "availability_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"availabilities_user_id_users_id_fk": {"name": "availabilities_user_id_users_id_fk", "tableFrom": "availabilities", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_availabilities": {"name": "users_can_view_availabilities", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tuser_id = auth.uid()\n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 \n\t\t\t\tFROM user_districts ud_current\n\t\t\t\tJOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id\n\t\t\t\tWHERE ud_current.user_id = auth.uid()\n\t\t\t\tAND ud_target.user_id = availabilities.user_id\n\t\t\t)\n\t\t"}, "users_can_manage_their_availabilities": {"name": "users_can_manage_their_availabilities", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tuser_id = auth.uid()\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_assignments": {"name": "case_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"case_assignment_unique": {"name": "case_assignment_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "case_assignment_deleted_idx": {"name": "case_assignment_deleted_idx", "columns": [{"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_assignment_user_deleted_idx": {"name": "case_assignment_user_deleted_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_assignment_case_idx": {"name": "case_assignment_case_idx", "columns": [{"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_assignment_user_case_deleted_idx": {"name": "case_assignment_user_case_deleted_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"case_assignments_user_id_users_id_fk": {"name": "case_assignments_user_id_users_id_fk", "tableFrom": "case_assignments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "case_assignments_case_id_cases_id_fk": {"name": "case_assignments_case_id_cases_id_fk", "tableFrom": "case_assignments", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "case_assignments_deleted_by_users_id_fk": {"name": "case_assignments_deleted_by_users_id_fk", "tableFrom": "case_assignments", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_case_assignments_based_on_role": {"name": "users_can_view_case_assignments_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tuser_id = auth.uid()\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_create_case_assignments": {"name": "authorized_roles_can_create_case_assignments", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_update_case_assignments": {"name": "authorized_roles_can_update_case_assignments", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "only_superuser_can_delete_case_assignments": {"name": "only_superuser_can_delete_case_assignments", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_details": {"name": "case_details", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"case_detail_key_idx": {"name": "case_detail_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_detail_deleted_idx": {"name": "case_detail_deleted_idx", "columns": [{"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"case_details_case_id_cases_id_fk": {"name": "case_details_case_id_cases_id_fk", "tableFrom": "case_details", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "case_details_deleted_by_users_id_fk": {"name": "case_details_deleted_by_users_id_fk", "tableFrom": "case_details", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_workflow_step_status": {"name": "case_workflow_step_status", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "case_workflow_id": {"name": "case_workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "step_id": {"name": "step_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "case_workflow_status", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"case_step_status_unique": {"name": "case_step_status_unique", "columns": [{"expression": "case_workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "step_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "case_step_status_workflow_idx": {"name": "case_step_status_workflow_idx", "columns": [{"expression": "case_workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_step_status_status_idx": {"name": "case_step_status_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"case_workflow_step_status_case_workflow_id_case_workflows_id_fk": {"name": "case_workflow_step_status_case_workflow_id_case_workflows_id_fk", "tableFrom": "case_workflow_step_status", "tableTo": "case_workflows", "columnsFrom": ["case_workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "case_workflow_step_status_step_id_workflow_steps_id_fk": {"name": "case_workflow_step_status_step_id_workflow_steps_id_fk", "tableFrom": "case_workflow_step_status", "tableTo": "workflow_steps", "columnsFrom": ["step_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.case_workflows": {"name": "case_workflows", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"case_workflows_unique": {"name": "case_workflows_unique", "columns": [{"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "case_workflows_status_idx": {"name": "case_workflows_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"case_workflows_case_id_cases_id_fk": {"name": "case_workflows_case_id_cases_id_fk", "tableFrom": "case_workflows", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "case_workflows_workflow_id_workflows_id_fk": {"name": "case_workflows_workflow_id_workflows_id_fk", "tableFrom": "case_workflows", "tableTo": "workflows", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cases": {"name": "cases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "status": {"name": "status", "type": "case_status", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "case_priority", "primaryKey": false, "notNull": true, "default": "'MEDIUM'"}, "case_type": {"name": "case_type", "type": "case_type", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "iep_status": {"name": "iep_status", "type": "iep_status", "primaryKey": false, "notNull": true}, "iep_start_date": {"name": "iep_start_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "iep_end_date": {"name": "iep_end_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "date_of_consent": {"name": "date_of_consent", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "evaluation_due_date": {"name": "evaluation_due_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "meeting_date": {"name": "meeting_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"case_priority_status_idx": {"name": "case_priority_status_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_student_idx": {"name": "case_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_type_idx": {"name": "case_type_idx", "columns": [{"expression": "case_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_active_idx": {"name": "case_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_deleted_idx": {"name": "case_deleted_idx", "columns": [{"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_eval_due_date_idx": {"name": "case_eval_due_date_idx", "columns": [{"expression": "evaluation_due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "case_student_deleted_idx": {"name": "case_student_deleted_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cases_student_id_students_id_fk": {"name": "cases_student_id_students_id_fk", "tableFrom": "cases", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cases_deleted_by_users_id_fk": {"name": "cases_deleted_by_users_id_fk", "tableFrom": "cases", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_cases_based_on_role": {"name": "users_can_view_cases_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM case_assignments ca\n\t\t\t\t\tWHERE ca.user_id = auth.uid()\n\t\t\t\t\tAND ca.case_id = cases.id\n\t\t\t\t\tAND ca.is_deleted = false\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\tWHERE se.student_id = student_id\n\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\tWHERE se.student_id = student_id\n\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_create_cases": {"name": "authorized_roles_can_create_cases", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\tOR\n\t\t(\n\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\tAND (\n\t\t\t\t(\n\t\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\t\tAND EXISTS (\n\t\t\t\t\t\tSELECT 1\n\t\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\t\tWHERE se.student_id = student_id\n\t\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t\tOR\n\t\t\t\t(\n\t\t\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\t\t\tAND EXISTS (\n\t\t\t\t\t\t\tSELECT 1\n\t\t\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\t\t\tWHERE se.student_id = student_id\n\t\t\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t)\n\t\t)\n\t"}, "users_can_update_accessible_cases": {"name": "users_can_update_accessible_cases", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\tOR\n\t\t(\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\tAND EXISTS (\n\t\t\t\tSELECT 1\n\t\t\t\tFROM case_assignments ca\n\t\t\t\tWHERE ca.user_id = auth.uid()\n\t\t\t\tAND ca.case_id = cases.id\n\t\t\t\tAND ca.is_deleted = false\n\t\t\t)\n\t\t)\n\t\tOR\n\t\t(\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\tAND EXISTS (\n\t\t\t\tSELECT 1\n\t\t\t\tFROM student_enrollments se\n\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\tWHERE se.student_id = student_id\n\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t)\n\t\t)\n\t\tOR\n\t\t(\n\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\tWHERE se.student_id = student_id\n\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t)\n\t\t)\n\t"}, "only_superuser_can_delete_cases": {"name": "only_superuser_can_delete_cases", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.district_availabilities": {"name": "district_availabilities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "availability_type", "primaryKey": false, "notNull": true, "default": "'EVALUATION'"}, "day": {"name": "day", "type": "day_of_week", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "time_zone": {"name": "time_zone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"district_avail_district_idx": {"name": "district_avail_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_avail_day_idx": {"name": "district_avail_day_idx", "columns": [{"expression": "day", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_avail_type_idx": {"name": "district_avail_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_avail_active_idx": {"name": "district_avail_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_avail_unique": {"name": "district_avail_unique", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "day", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"district_availabilities_district_id_districts_id_fk": {"name": "district_availabilities_district_id_districts_id_fk", "tableFrom": "district_availabilities", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "district_availabilities_created_by_users_id_fk": {"name": "district_availabilities_created_by_users_id_fk", "tableFrom": "district_availabilities", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_district_availabilities": {"name": "users_can_view_district_availabilities", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\tAND ud.district_id = district_id\n\t\t\t)\n\t\t"}, "authorized_roles_can_manage_district_availabilities": {"name": "authorized_roles_can_manage_district_availabilities", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM user_districts ud\n          WHERE ud.user_id = auth.uid()\n          AND ud.district_id = district_id\n        )\n      )\n    )\n  \n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.district_blocked_dates": {"name": "district_blocked_dates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "block_type": {"name": "block_type", "type": "blocked_date_type", "primaryKey": false, "notNull": true, "default": "'OTHER'"}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "is_recurring": {"name": "is_recurring", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "recurrence_pattern": {"name": "recurrence_pattern", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "affects_types": {"name": "affects_types", "type": "text[]", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"district_blocked_district_idx": {"name": "district_blocked_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_blocked_dates_idx": {"name": "district_blocked_dates_idx", "columns": [{"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_blocked_type_idx": {"name": "district_blocked_type_idx", "columns": [{"expression": "block_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_blocked_active_idx": {"name": "district_blocked_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_blocked_recurring_idx": {"name": "district_blocked_recurring_idx", "columns": [{"expression": "is_recurring", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"district_blocked_dates_district_id_districts_id_fk": {"name": "district_blocked_dates_district_id_districts_id_fk", "tableFrom": "district_blocked_dates", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "district_blocked_dates_created_by_users_id_fk": {"name": "district_blocked_dates_created_by_users_id_fk", "tableFrom": "district_blocked_dates", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_district_blocked_dates": {"name": "users_can_view_district_blocked_dates", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\tAND ud.district_id = district_id\n\t\t\t)\n\t\t"}, "authorized_roles_can_manage_district_blocked_dates": {"name": "authorized_roles_can_manage_district_blocked_dates", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM user_districts ud\n          WHERE ud.user_id = auth.uid()\n          AND ud.district_id = district_id\n        )\n      )\n    )\n  \n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.district_preferences": {"name": "district_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "preference_category", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "preference_type", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "last_modified_by": {"name": "last_modified_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"district_preference_unique": {"name": "district_preference_unique", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "district_pref_district_idx": {"name": "district_pref_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_pref_category_idx": {"name": "district_pref_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "district_pref_key_idx": {"name": "district_pref_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"district_preferences_district_id_districts_id_fk": {"name": "district_preferences_district_id_districts_id_fk", "tableFrom": "district_preferences", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "district_preferences_last_modified_by_users_id_fk": {"name": "district_preferences_last_modified_by_users_id_fk", "tableFrom": "district_preferences", "tableTo": "users", "columnsFrom": ["last_modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_manage_district_preferences_select": {"name": "users_can_manage_district_preferences_select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n        OR\n        EXISTS (\n          SELECT 1 FROM user_districts ud \n          WHERE ud.user_id = auth.uid() \n          AND ud.district_id = \"district_preferences\".district_id\n        )\n      "}, "users_can_manage_district_preferences_all": {"name": "users_can_manage_district_preferences_all", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM user_districts ud\n          WHERE ud.user_id = auth.uid()\n          AND ud.district_id = \"district_preferences\".district_id\n        )\n      )\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.districts": {"name": "districts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "district_type", "primaryKey": false, "notNull": true, "default": "'UNIFIED_DISTRICT'"}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "nces_id": {"name": "nces_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "state_id": {"name": "state_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "county": {"name": "county", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "num_schools": {"name": "num_schools", "type": "integer", "primaryKey": false, "notNull": false}, "num_students": {"name": "num_students", "type": "integer", "primaryKey": false, "notNull": false}, "invoice_email": {"name": "invoice_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address_id": {"name": "address_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"districts_address_id_addresses_id_fk": {"name": "districts_address_id_addresses_id_fk", "tableFrom": "districts", "tableTo": "addresses", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_districts_select": {"name": "all_users_can_view_districts_select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "auth.uid() IS NOT NULL"}, "only_superuser_can_create_districts": {"name": "only_superuser_can_create_districts", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}, "authorized_roles_can_update_districts": {"name": "authorized_roles_can_update_districts", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM user_districts ud\n          WHERE ud.user_id = auth.uid()\n          AND ud.district_id = id\n        )\n      )\n    )\n  "}, "only_superusers_can_delete_districts": {"name": "only_superusers_can_delete_districts", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assessment_session_id": {"name": "assessment_session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "test_administration_id": {"name": "test_administration_id", "type": "uuid", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "document_category", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "uploaded_user_id": {"name": "uploaded_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"document_student_idx": {"name": "document_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_category_idx": {"name": "document_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_session_idx": {"name": "document_session_idx", "columns": [{"expression": "assessment_session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_test_admin_idx": {"name": "document_test_admin_idx", "columns": [{"expression": "test_administration_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documents_student_id_students_id_fk": {"name": "documents_student_id_students_id_fk", "tableFrom": "documents", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "documents_uploaded_user_id_users_id_fk": {"name": "documents_uploaded_user_id_users_id_fk", "tableFrom": "documents", "tableTo": "users", "columnsFrom": ["uploaded_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_documents_for_accessible_students": {"name": "users_can_view_documents_for_accessible_students", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t"}, "users_can_upload_documents_for_accessible_students": {"name": "users_can_upload_documents_for_accessible_students", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\tauth.uid() IS NOT NULL\n\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t"}, "users_can_update_their_documents": {"name": "users_can_update_their_documents", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tuploaded_user_id = auth.uid()\n\t\t"}, "only_superuser_can_delete_documents": {"name": "only_superuser_can_delete_documents", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback_files": {"name": "feedback_files", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "feedback_id": {"name": "feedback_id", "type": "uuid", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"feedback_files_feedback_id_feedback_id_fk": {"name": "feedback_files_feedback_id_feedback_id_fk", "tableFrom": "feedback_files", "tableTo": "feedback", "columnsFrom": ["feedback_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Anyone can get feedback file data": {"name": "Anyone can get feedback file data", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "true"}, "Only authenticated user can create feedback file data": {"name": "Only authenticated user can create feedback file data", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "EXISTS (\n      SELECT 1 FROM feedback f\n      WHERE f.id = feedback_files.feedback_id AND f.user_id = auth.uid()\n    )"}, "Users can delete their own feedback files or super users can delete any": {"name": "Users can delete their own feedback files or super users can delete any", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM feedback f\n\t\t\t\tWHERE f.id = feedback_files.feedback_id AND f.user_id = auth.uid()\n\t\t\t)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "feedback_type", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "primaryKey": false, "notNull": true, "default": "'OPEN'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "issue_type": {"name": "issue_type", "type": "issue_type", "primaryKey": false, "notNull": true, "default": "'OTHER'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_feedback_rating": {"name": "idx_feedback_rating", "columns": [{"expression": "rating", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_feedback_type": {"name": "idx_feedback_type", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_feedback_status": {"name": "idx_feedback_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_feedback_issue_type": {"name": "idx_feedback_issue_type", "columns": [{"expression": "issue_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"feedback_user_id_users_id_fk": {"name": "feedback_user_id_users_id_fk", "tableFrom": "feedback", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Anyone can get feedback data": {"name": "Anyone can get feedback data", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"], "using": "true"}, "Only authenticated user can create feedback data": {"name": "Only authenticated user can create feedback data", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "user_id = auth.uid()"}, "Users can update their own feedback or super users can update any": {"name": "Users can update their own feedback or super users can update any", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "user_id = auth.uid() OR \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}, "Users can delete their own feedback or super users can delete any": {"name": "Users can delete their own feedback or super users can delete any", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "user_id = auth.uid() OR \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitation_schools": {"name": "invitation_schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "invitation_id": {"name": "invitation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"invitation_school_unique": {"name": "invitation_school_unique", "columns": [{"expression": "invitation_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "invitation_schools_invitation_idx": {"name": "invitation_schools_invitation_idx", "columns": [{"expression": "invitation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_schools_school_idx": {"name": "invitation_schools_school_idx", "columns": [{"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitation_schools_invitation_id_invitations_id_fk": {"name": "invitation_schools_invitation_id_invitations_id_fk", "tableFrom": "invitation_schools", "tableTo": "invitations", "columnsFrom": ["invitation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitation_schools_school_id_schools_id_fk": {"name": "invitation_schools_school_id_schools_id_fk", "tableFrom": "invitation_schools", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_invitation_schools": {"name": "users_can_view_invitation_schools", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM invitations i\n\t\t\t\tWHERE i.id = invitation_id\n\t\t\t\tAND (\n\t\t\t\t\ti.email = auth.email()\n\t\t\t\t\tOR\n\t\t\t\t\t(\n\t\t\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\t\t\t\tAND EXISTS (\n\t\t\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\t\t\tAND ud.district_id = i.district_id\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "users_can_manage_invitation_schools": {"name": "users_can_manage_invitation_schools", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM invitations i\n\t\t\t\tWHERE i.id = invitation_id\n\t\t\t\tAND \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\t\tAND ud.district_id = i.district_id\n\t\t\t\t)\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "uuid", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invitation_status", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "(NOW() + INTERVAL '7 days')"}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "rejected_at": {"name": "rejected_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_by_id": {"name": "invited_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"invitation_status_idx": {"name": "invitation_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_email_idx": {"name": "invitation_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_district_idx": {"name": "invitation_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_expires_idx": {"name": "invitation_expires_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_token_idx": {"name": "invitation_token_idx", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_active_idx": {"name": "invitation_active_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitations_role_id_roles_id_fk": {"name": "invitations_role_id_roles_id_fk", "tableFrom": "invitations", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_district_id_districts_id_fk": {"name": "invitations_district_id_districts_id_fk", "tableFrom": "invitations", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_inviter_id_users_id_fk": {"name": "invitations_inviter_id_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_invited_by_id_users_id_fk": {"name": "invitations_invited_by_id_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_invitations": {"name": "users_can_view_invitations", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\temail = auth.email()\n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\tAND ud.district_id = district_id\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_create_invitations": {"name": "authorized_roles_can_create_invitations", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\tAND EXISTS (\n\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\tAND ud.district_id = district_id\n\t\t\t)\n\t\t\tAND inviter_id = auth.uid()\n\t\t"}, "users_can_update_relevant_invitations": {"name": "users_can_update_relevant_invitations", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\temail = auth.email()\n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\tAND ud.district_id = district_id\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "only_superuser_can_delete_invitations": {"name": "only_superuser_can_delete_invitations", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.join_requests": {"name": "join_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "district_name": {"name": "district_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_join_requests": {"name": "users_can_view_join_requests", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t"}, "users_can_manage_join_requests": {"name": "users_can_manage_join_requests", "as": "PERMISSIVE", "for": "INSERT", "to": ["anon"], "withCheck": "\n\t\ttrue\n\t"}, "superuser_can_manage_join_requests": {"name": "superuser_can_manage_join_requests", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.languages": {"name": "languages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "emoji": {"name": "emoji", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}}, "indexes": {"language_code_unique": {"name": "language_code_unique", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "language_name_idx": {"name": "language_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"languages_code_unique": {"name": "languages_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {"all_users_can_view_languages": {"name": "all_users_can_view_languages", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "notification_type", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "category": {"name": "category", "type": "notification_category_type", "primaryKey": false, "notNull": true, "default": "'GENERAL'"}, "read_at": {"name": "read_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "archived_at": {"name": "archived_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now() + interval '1 month'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_notification_user_read": {"name": "idx_notification_user_read", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notification_user_archived": {"name": "idx_notification_user_archived", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_archived", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"only_authenticated_users_can_get_their_own_notifications": {"name": "only_authenticated_users_can_get_their_own_notifications", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "auth.uid() = user_id"}, "only_authenticated_users_can_update_their_own_notifications": {"name": "only_authenticated_users_can_update_their_own_notifications", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "auth.uid() = user_id"}, "only_authenticated_users_can_delete_their_own_notifications": {"name": "only_authenticated_users_can_delete_their_own_notifications", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "auth.uid() = user_id"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.parents": {"name": "parents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "generated": {"as": "concat_names(\"parents\".\"first_name\", \"parents\".\"middle_name\", \"parents\".\"last_name\")", "type": "stored"}}, "primary_email": {"name": "primary_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "secondary_email": {"name": "secondary_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "primary_phone": {"name": "primary_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "secondary_phone": {"name": "secondary_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "relationship_type": {"name": "relationship_type", "type": "parent_relationship", "primaryKey": false, "notNull": true, "default": "'UNKNOWN'"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"parent_name_idx": {"name": "parent_name_idx", "columns": [{"expression": "full_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "parent_email_idx": {"name": "parent_email_idx", "columns": [{"expression": "primary_email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "parent_deleted_idx": {"name": "parent_deleted_idx", "columns": [{"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"parents_deleted_by_users_id_fk": {"name": "parents_deleted_by_users_id_fk", "tableFrom": "parents", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_parents_based_on_student_access": {"name": "users_can_view_parents_based_on_student_access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM student_parents sp\n\t\t\t\tWHERE sp.parent_id = parents.id\n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = sp.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = sp.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = sp.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t)\n\t\t"}, "users_can_create_parents_for_accessible_students": {"name": "users_can_create_parents_for_accessible_students", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "auth.uid() IS NOT NULL"}, "users_can_update_parents_for_accessible_students": {"name": "users_can_update_parents_for_accessible_students", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM student_parents sp\n\t\t\t\tWHERE sp.parent_id = parents.id\n\t\t\t\tAND \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = sp.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = sp.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = sp.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t)\n\t\t"}, "only_superuser_can_delete_parents": {"name": "only_superuser_can_delete_parents", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"permission_name_idx": {"name": "permission_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_permissions_select": {"name": "all_users_can_view_permissions_select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.plans": {"name": "plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "plan_type", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "plan_status", "primaryKey": false, "notNull": true}, "expiration_date": {"name": "expiration_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"plan_student_idx": {"name": "plan_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "plan_case_idx": {"name": "plan_case_idx", "columns": [{"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "plan_type_status_idx": {"name": "plan_type_status_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "plan_expiration_idx": {"name": "plan_expiration_idx", "columns": [{"expression": "expiration_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "plan_deleted_idx": {"name": "plan_deleted_idx", "columns": [{"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"plans_student_id_students_id_fk": {"name": "plans_student_id_students_id_fk", "tableFrom": "plans", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "plans_case_id_cases_id_fk": {"name": "plans_case_id_cases_id_fk", "tableFrom": "plans", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "plans_deleted_by_users_id_fk": {"name": "plans_deleted_by_users_id_fk", "tableFrom": "plans", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_plans_for_accessible_cases": {"name": "users_can_view_plans_for_accessible_cases", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM case_assignments ca\n\t\t\t\t\tWHERE ca.user_id = auth.uid()\n\t\t\t\t\tAND ca.case_id = plans.case_id\n\t\t\t\t\tAND ca.is_deleted = false\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\tWHERE se.student_id = plans.student_id\n\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\tWHERE se.student_id = plans.student_id\n\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "users_can_manage_plans_for_accessible_cases": {"name": "users_can_manage_plans_for_accessible_cases", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM case_assignments ca\n\t\t\t\t\tWHERE ca.user_id = auth.uid()\n\t\t\t\t\tAND ca.case_id = plans.case_id\n\t\t\t\t\tAND ca.is_deleted = false\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\tWHERE se.student_id = plans.student_id\n\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\tWHERE se.student_id = plans.student_id\n\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"role_permission_role_idx": {"name": "role_permission_role_idx", "columns": [{"expression": "role_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "role_permission_permission_idx": {"name": "role_permission_permission_idx", "columns": [{"expression": "permission_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"role_permissions_role_id_roles_id_fk": {"name": "role_permissions_role_id_roles_id_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "role_permissions_permission_id_permissions_id_fk": {"name": "role_permissions_permission_id_permissions_id_fk", "tableFrom": "role_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_role_permissions_select": {"name": "all_users_can_view_role_permissions_select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "name": {"name": "name", "type": "role", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"role_name_idx": {"name": "role_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_roles_select": {"name": "all_users_can_view_roles_select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schools": {"name": "schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "school_type", "primaryKey": false, "notNull": true}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "nces_id": {"name": "nces_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "address_id": {"name": "address_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"school_district_idx": {"name": "school_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"schools_district_id_districts_id_fk": {"name": "schools_district_id_districts_id_fk", "tableFrom": "schools", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "schools_address_id_addresses_id_fk": {"name": "schools_address_id_addresses_id_fk", "tableFrom": "schools", "tableTo": "addresses", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_schools_in_their_districts": {"name": "users_can_view_schools_in_their_districts", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\tAND ud.district_id = district_id\n\t\t\t)\n\t\t"}, "authorized_roles_can_create_schools": {"name": "authorized_roles_can_create_schools", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\tAND ud.district_id = district_id\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_update_schools": {"name": "authorized_roles_can_update_schools", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\tAND ud.district_id = district_id\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "only_superuser_can_delete_schools": {"name": "only_superuser_can_delete_schools", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_step_dependencies": {"name": "workflow_step_dependencies", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "step_id": {"name": "step_id", "type": "uuid", "primaryKey": false, "notNull": true}, "depends_on_step_id": {"name": "depends_on_step_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"step_dependencies_unique": {"name": "step_dependencies_unique", "columns": [{"expression": "step_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "depends_on_step_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "step_dependencies_step_idx": {"name": "step_dependencies_step_idx", "columns": [{"expression": "step_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_step_dependencies_step_id_workflow_steps_id_fk": {"name": "workflow_step_dependencies_step_id_workflow_steps_id_fk", "tableFrom": "workflow_step_dependencies", "tableTo": "workflow_steps", "columnsFrom": ["step_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workflow_step_dependencies_depends_on_step_id_workflow_steps_id_fk": {"name": "workflow_step_dependencies_depends_on_step_id_workflow_steps_id_fk", "tableFrom": "workflow_step_dependencies", "tableTo": "workflow_steps", "columnsFrom": ["depends_on_step_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_enrollments": {"name": "student_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"student_enrollment_student_idx": {"name": "student_enrollment_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "student_enrollment_school_idx": {"name": "student_enrollment_school_idx", "columns": [{"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "student_enrollment_district_idx": {"name": "student_enrollment_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "student_enrollment_unique": {"name": "student_enrollment_unique", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"student_enrollments_student_id_students_id_fk": {"name": "student_enrollments_student_id_students_id_fk", "tableFrom": "student_enrollments", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_enrollments_school_id_schools_id_fk": {"name": "student_enrollments_school_id_schools_id_fk", "tableFrom": "student_enrollments", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "student_enrollments_district_id_districts_id_fk": {"name": "student_enrollments_district_id_districts_id_fk", "tableFrom": "student_enrollments", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_enrollments_for_accessible_students": {"name": "users_can_view_enrollments_for_accessible_students", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1\n\t\t\t\tFROM user_districts ud\n\t\t\t\tJOIN schools s ON s.district_id = ud.district_id\n\t\t\t\tWHERE s.id = student_enrollments.school_id\n\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t)\n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1\n\t\t\t\tFROM user_schools us\n\t\t\t\tWHERE us.school_id = student_enrollments.school_id\n\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t)\n\t\t"}, "users_can_manage_enrollments_for_accessible_students": {"name": "users_can_manage_enrollments_for_accessible_students", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\tNOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND (\n\t\t\t\t\tEXISTS (\n\t\t\t\t\t\tSELECT 1\n\t\t\t\t\t\tFROM user_districts ud\n\t\t\t\t\t\tJOIN schools s ON s.district_id = ud.district_id\n\t\t\t\t\t\tWHERE s.id = student_enrollments.school_id\n\t\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t\t)\n\t\t\t\t\tOR\n\t\t\t\t\tEXISTS (\n\t\t\t\t\t\tSELECT 1\n\t\t\t\t\t\tFROM user_schools us\n\t\t\t\t\t\tWHERE us.school_id = student_enrollments.school_id\n\t\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_languages": {"name": "student_languages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "language_id": {"name": "language_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"student_language_unique": {"name": "student_language_unique", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "language_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "student_language_primary_idx": {"name": "student_language_primary_idx", "columns": [{"expression": "is_primary", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"student_languages_student_id_students_id_fk": {"name": "student_languages_student_id_students_id_fk", "tableFrom": "student_languages", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_languages_language_id_languages_id_fk": {"name": "student_languages_language_id_languages_id_fk", "tableFrom": "student_languages", "tableTo": "languages", "columnsFrom": ["language_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_student_languages": {"name": "users_can_view_student_languages", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t"}, "users_can_manage_student_languages": {"name": "users_can_manage_student_languages", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_parents": {"name": "student_parents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_primary_contact": {"name": "is_primary_contact", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_pickup_authorization": {"name": "has_pickup_authorization", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"student_parent_unique": {"name": "student_parent_unique", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "student_parent_primary_idx": {"name": "student_parent_primary_idx", "columns": [{"expression": "is_primary_contact", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"student_parents_parent_id_parents_id_fk": {"name": "student_parents_parent_id_parents_id_fk", "tableFrom": "student_parents", "tableTo": "parents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_parents_student_id_students_id_fk": {"name": "student_parents_student_id_students_id_fk", "tableFrom": "student_parents", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_student_parent_relationships": {"name": "users_can_view_student_parent_relationships", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_parents.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_parents.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_parents.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t"}, "users_can_manage_student_parent_relationships": {"name": "users_can_manage_student_parent_relationships", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_parents.student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_parents.student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_parents.student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.students": {"name": "students", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "generated": {"as": "concat_names(\"students\".\"first_name\", \"students\".\"middle_name\", \"students\".\"last_name\")", "type": "stored"}}, "preferred_name": {"name": "preferred_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "student_id_number": {"name": "student_id_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "gender", "primaryKey": false, "notNull": true}, "primary_school_id": {"name": "primary_school_id", "type": "uuid", "primaryKey": false, "notNull": false}, "private_school": {"name": "private_school", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "enrollment_status": {"name": "enrollment_status", "type": "enrollment_status", "primaryKey": false, "notNull": true, "default": "'ENROLLED'"}, "emergency_contact_name": {"name": "emergency_contact_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "emergency_contact_phone": {"name": "emergency_contact_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"student_id_school_unique": {"name": "student_id_school_unique", "columns": [{"expression": "student_id_number", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "primary_school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "student_enrollment_status_idx": {"name": "student_enrollment_status_idx", "columns": [{"expression": "enrollment_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "student_deleted_idx": {"name": "student_deleted_idx", "columns": [{"expression": "is_deleted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "student_name_idx": {"name": "student_name_idx", "columns": [{"expression": "full_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"students_primary_school_id_schools_id_fk": {"name": "students_primary_school_id_schools_id_fk", "tableFrom": "students", "tableTo": "schools", "columnsFrom": ["primary_school_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "students_deleted_by_users_id_fk": {"name": "students_deleted_by_users_id_fk", "tableFrom": "students", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_students_based_on_role": {"name": "users_can_view_students_based_on_role", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\tWHERE se.student_id = students.id\n\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM case_assignments ca\n\t\t\t\t\tJOIN cases c ON c.id = ca.case_id\n\t\t\t\t\tWHERE c.student_id = students.id\n\t\t\t\t\tAND ca.user_id = auth.uid()\n\t\t\t\t\tAND ca.is_deleted = false\n\t\t\t\t\tAND c.is_deleted = false\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1\n\t\t\t\t\tFROM student_enrollments se\n\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\tWHERE se.student_id = students.id\n\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_create_students": {"name": "authorized_roles_can_create_students", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM schools s\n\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\tWHERE s.id = primary_school_id\n\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('ASSISTANT')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\t\tSELECT 1 FROM user_schools us\n\t\t\t\t\t\tWHERE us.user_id = auth.uid()\n\t\t\t\t\t\tAND us.school_id = primary_school_id\n\t\t\t\t\t)\n\t\t\t)\n\t\t"}, "authorized_roles_can_update_students": {"name": "authorized_roles_can_update_students", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM student_enrollments se\n\t\t\t\t\tJOIN schools s ON s.id = se.school_id\n\t\t\t\t\tJOIN user_districts ud ON ud.district_id = s.district_id\n\t\t\t\t\tWHERE se.student_id = students.id\n\t\t\t\t\tAND ud.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('ASSISTANT', 'SCHOOL_COORDINATOR', 'SCHOOL_ADMIN')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM student_enrollments se\n\t\t\t\t\tJOIN user_schools us ON us.school_id = se.school_id\n\t\t\t\t\tWHERE se.student_id = students.id\n\t\t\t\t\tAND us.user_id = auth.uid()\n\t\t\t\t)\n\t\t\t)\n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST')\n    )\n  \n\t\t\t\tAND EXISTS (\n\t\t\t\t\tSELECT 1 FROM cases c\n\t\t\t\t\tJOIN case_assignments ca ON ca.case_id = c.id\n\t\t\t\t\tWHERE c.student_id = students.id\n\t\t\t\t\tAND ca.user_id = auth.uid()\n\t\t\t\t\tAND ca.is_deleted = false\n\t\t\t\t\tAND c.is_deleted = false\n\t\t\t\t)\n\t\t\t)\n\t\t"}, "only_superuser_can_delete_students": {"name": "only_superuser_can_delete_students", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_dependencies": {"name": "task_dependencies", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "predecessor_task_id": {"name": "predecessor_task_id", "type": "uuid", "primaryKey": false, "notNull": true}, "successor_task_id": {"name": "successor_task_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_dependencies_predecessor_task_id_tasks_id_fk": {"name": "task_dependencies_predecessor_task_id_tasks_id_fk", "tableFrom": "task_dependencies", "tableTo": "tasks", "columnsFrom": ["predecessor_task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_dependencies_successor_task_id_tasks_id_fk": {"name": "task_dependencies_successor_task_id_tasks_id_fk", "tableFrom": "task_dependencies", "tableTo": "tasks", "columnsFrom": ["successor_task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_history": {"name": "task_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "task_id": {"name": "task_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "task_history_action", "primaryKey": false, "notNull": true}, "previous_status": {"name": "previous_status", "type": "task_status", "primaryKey": false, "notNull": false}, "new_status": {"name": "new_status", "type": "task_status", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_history_task_id_tasks_id_fk": {"name": "task_history_task_id_tasks_id_fk", "tableFrom": "task_history", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "task_history_user_id_users_id_fk": {"name": "task_history_user_id_users_id_fk", "tableFrom": "task_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "task_type": {"name": "task_type", "type": "task_type", "primaryKey": false, "notNull": true}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": false}, "workflow_step_id": {"name": "workflow_step_id", "type": "uuid", "primaryKey": false, "notNull": false}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": false}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_by_id": {"name": "assigned_by_id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "null"}, "status": {"name": "status", "type": "task_status", "primaryKey": false, "notNull": true, "default": "'TODO'"}, "priority": {"name": "priority", "type": "task_priority", "primaryKey": false, "notNull": true, "default": "'MEDIUM'"}, "due_date": {"name": "due_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "rejected_at": {"name": "rejected_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "cancelled_at": {"name": "cancelled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"task_assigned_to_idx": {"name": "task_assigned_to_idx", "columns": [{"expression": "assigned_to_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_status_priority_idx": {"name": "task_status_priority_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_due_date_idx": {"name": "task_due_date_idx", "columns": [{"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_case_idx": {"name": "task_case_idx", "columns": [{"expression": "case_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_student_idx": {"name": "task_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_district_idx": {"name": "task_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_type_idx": {"name": "task_type_idx", "columns": [{"expression": "task_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_completed_idx": {"name": "task_completed_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_assigned_status_due_idx": {"name": "task_assigned_status_due_idx", "columns": [{"expression": "assigned_to_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_district_priority_idx": {"name": "task_district_priority_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_active_idx": {"name": "task_active_idx", "columns": [{"expression": "assigned_to_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "task_overdue_idx": {"name": "task_overdue_idx", "columns": [{"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tasks_case_id_cases_id_fk": {"name": "tasks_case_id_cases_id_fk", "tableFrom": "tasks", "tableTo": "cases", "columnsFrom": ["case_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_student_id_students_id_fk": {"name": "tasks_student_id_students_id_fk", "tableFrom": "tasks", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_school_id_schools_id_fk": {"name": "tasks_school_id_schools_id_fk", "tableFrom": "tasks", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_workflow_step_id_workflow_steps_id_fk": {"name": "tasks_workflow_step_id_workflow_steps_id_fk", "tableFrom": "tasks", "tableTo": "workflow_steps", "columnsFrom": ["workflow_step_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_district_id_districts_id_fk": {"name": "tasks_district_id_districts_id_fk", "tableFrom": "tasks", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_assigned_to_id_users_id_fk": {"name": "tasks_assigned_to_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["assigned_to_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_assigned_by_id_users_id_fk": {"name": "tasks_assigned_by_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["assigned_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_tasks_in_their_scope": {"name": "users_can_view_tasks_in_their_scope", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\tassigned_to_id = auth.uid()\n\t\t\tOR\n\t\t\tassigned_by_id = auth.uid()\n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\tAND ud.district_id = district_id\n\t\t\t)\n\t\t"}, "users_can_create_tasks_in_their_scope": {"name": "users_can_create_tasks_in_their_scope", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "\n\t\t\tauth.uid() IS NOT NULL\n\t\t\tAND EXISTS (\n\t\t\t\tSELECT 1 FROM user_districts ud\n\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\tAND ud.district_id = district_id\n\t\t\t)\n\t\t\tAND (\n\t\t\t\tcase_id IS NULL\n\t\t\t\tOR (\n\t\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\t\t\tOR\n\t\t\t\t\t(\n\t\t\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n\t\t\t\t\t\tAND EXISTS (\n\t\t\t\t\t\t\tSELECT 1\n\t\t\t\t\t\t\tFROM case_assignments ca\n\t\t\t\t\t\t\tWHERE ca.user_id = auth.uid()\n\t\t\t\t\t\t\tAND ca.case_id = case_id\n\t\t\t\t\t\t\tAND ca.is_deleted = false\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t\tOR\n\t\t\t\t\t(\n\t\t\t\t\t\tEXISTS (\n\t\t\t\t\t\t\tSELECT 1\n\t\t\t\t\t\t\tFROM user_districts ud\n\t\t\t\t\t\t\tWHERE ud.user_id = auth.uid()\n\t\t\t\t\t\t\tAND ud.district_id = district_id\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t)\n\t\t\tAND (\n\t\t\t\tstudent_id IS NULL\n\t\t\t\tOR \n    (\n      \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN schools s ON s.id = se.school_id\n          JOIN user_districts ud ON ud.district_id = s.district_id\n          WHERE se.student_id = student_id\n          AND ud.user_id = auth.uid()\n        )\n      )\n      OR\n      (\n        \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM cases c\n          JOIN case_assignments ca ON ca.case_id = c.id\n          WHERE c.student_id = student_id\n          AND ca.user_id = auth.uid()\n          AND ca.is_deleted = false\n          AND c.is_deleted = false\n        )\n      )\n      OR\n      (\n        NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')\n    )\n  \n        AND NOT \n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')\n    )\n  \n        AND EXISTS (\n          SELECT 1\n          FROM student_enrollments se\n          JOIN user_schools us ON us.school_id = se.school_id\n          WHERE se.student_id = student_id\n          AND us.user_id = auth.uid()\n        )\n      )\n    )\n  \n\t\t\t)\n\t\t"}, "users_can_update_assigned_tasks": {"name": "users_can_update_assigned_tasks", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\t\tassigned_to_id = auth.uid()\n\t\t\tOR\n\t\t\tassigned_by_id = auth.uid()\n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t"}, "only_superuser_can_delete_tasks": {"name": "only_superuser_can_delete_tasks", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_districts": {"name": "user_districts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "district_id": {"name": "district_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_district_unique": {"name": "user_district_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_district_user_idx": {"name": "user_district_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_district_district_idx": {"name": "user_district_district_idx", "columns": [{"expression": "district_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_districts_district_id_districts_id_fk": {"name": "user_districts_district_id_districts_id_fk", "tableFrom": "user_districts", "tableTo": "districts", "columnsFrom": ["district_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_districts_user_id_users_id_fk": {"name": "user_districts_user_id_users_id_fk", "tableFrom": "user_districts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_district_associations": {"name": "users_can_view_district_associations", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tuser_districts.user_id = auth.uid()\n\t\t"}, "authorized_roles_can_manage_district_associations": {"name": "authorized_roles_can_manage_district_associations", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\t(\n\t\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\t\tAND user_districts.user_id = auth.uid()\n\t\t\t)\n\t\t"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_notification_preferences": {"name": "user_notification_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "notification_type": {"name": "notification_type", "type": "notification_type", "primaryKey": false, "notNull": true}, "channel": {"name": "channel", "type": "notification_channel", "primaryKey": false, "notNull": true}, "is_enabled": {"name": "is_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_notification_preferences_user_idx": {"name": "user_notification_preferences_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_notification_preferences_type_idx": {"name": "user_notification_preferences_type_idx", "columns": [{"expression": "notification_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_notification_preferences_channel_idx": {"name": "user_notification_preferences_channel_idx", "columns": [{"expression": "channel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_notification_preferences_user_id_users_id_fk": {"name": "user_notification_preferences_user_id_users_id_fk", "tableFrom": "user_notification_preferences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_notification_type_channel_unique": {"name": "user_notification_type_channel_unique", "nullsNotDistinct": false, "columns": ["user_id", "notification_type", "channel"]}}, "policies": {"only_authenticated_users_can_get_their_own_notification_preferences": {"name": "only_authenticated_users_can_get_their_own_notification_preferences", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "auth.uid() = user_id"}, "only_authenticated_users_can_update_their_own_notification_preferences": {"name": "only_authenticated_users_can_update_their_own_notification_preferences", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "auth.uid() = user_id"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_name": {"name": "role_name", "type": "role", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_role_unique": {"name": "user_role_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_role_role_idx": {"name": "user_role_role_idx", "columns": [{"expression": "role_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_role_user_name_idx": {"name": "user_role_user_name_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_role_id_roles_id_fk": {"name": "user_roles_role_id_roles_id_fk", "tableFrom": "user_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"all_users_can_view_user_roles_select": {"name": "all_users_can_view_user_roles_select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "true"}, "all_users_can_manage_user_roles_all": {"name": "all_users_can_manage_user_roles_all", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "auth.uid() IS NOT NULL"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_schools": {"name": "user_schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_school_unique": {"name": "user_school_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_school_user_idx": {"name": "user_school_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_school_school_idx": {"name": "user_school_school_idx", "columns": [{"expression": "school_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_schools_user_id_users_id_fk": {"name": "user_schools_user_id_users_id_fk", "tableFrom": "user_schools", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_schools_school_id_schools_id_fk": {"name": "user_schools_school_id_schools_id_fk", "tableFrom": "user_schools", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "generated": {"as": "concat_names(\"users\".\"first_name\", \"users\".\"middle_name\", \"users\".\"last_name\")", "type": "stored"}}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "is_onboarded": {"name": "is_onboarded", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_name_idx": {"name": "user_name_idx", "columns": [{"expression": "full_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_email_idx": {"name": "user_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_id_users_id_fk": {"name": "users_id_users_id_fk", "tableFrom": "users", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users_can_view_users_in_their_scope": {"name": "users_can_view_users_in_their_scope", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n\t\t\tid = auth.uid()\n\t\t\tOR\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\t\tOR\n\t\t\tEXISTS (\n\t\t\t\tSELECT 1 \n\t\t\t\tFROM user_districts ud_current\n\t\t\t\tJOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id\n\t\t\t\tWHERE ud_current.user_id = auth.uid()\n\t\t\t\tAND ud_target.user_id = users.id\n\t\t\t)\n\t\t"}, "users_cannot_be_created_directly": {"name": "users_cannot_be_created_directly", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "false"}, "authorized_roles_can_update_users": {"name": "authorized_roles_can_update_users", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "\n\t\tid = auth.uid()\n\t\tOR\n\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  \n\t\tOR\n\t\t(\n\t\t\t\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER')\n    )\n  \n\t\t\tAND EXISTS (\n\t\t\t\tSELECT 1 \n\t\t\t\tFROM user_districts ud_current\n\t\t\t\tJOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id\n\t\t\t\tWHERE ud_current.user_id = auth.uid()\n\t\t\t\tAND ud_target.user_id = users.id\n\t\t\t)\n\t\t)\n\t"}, "only_superuser_can_delete_users": {"name": "only_superuser_can_delete_users", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "\n    EXISTS (\n      SELECT 1\n      FROM user_roles\n      WHERE user_id = auth.uid()\n      AND role_name = 'SUPER_USER'\n    )\n  "}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_steps": {"name": "workflow_steps", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "step_number": {"name": "step_number", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "estimated_days": {"name": "estimated_days", "type": "integer", "primaryKey": false, "notNull": false}, "is_optional": {"name": "is_optional", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_steps_unique": {"name": "workflow_steps_unique", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "step_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "workflow_steps_workflow_idx": {"name": "workflow_steps_workflow_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_steps_workflow_id_workflows_id_fk": {"name": "workflow_steps_workflow_id_workflows_id_fk", "tableFrom": "workflow_steps", "tableTo": "workflows", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflows": {"name": "workflows", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "uuid_generate_v7()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'1.0'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflows_active_idx": {"name": "workflows_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.address_type": {"name": "address_type", "schema": "public", "values": ["PHYSICAL", "POSTAL", "BILLING", "OTHER"]}, "public.admin_status": {"name": "admin_status", "schema": "public", "values": ["PLANNED", "IN_PROGRESS", "COMPLETED", "DISCONTINUED", "INVALID", "PARTIAL"]}, "public.attendee_status": {"name": "attendee_status", "schema": "public", "values": ["PENDING", "ACCEPTED", "DECLINED", "TENTATIVE"]}, "public.availability_type": {"name": "availability_type", "schema": "public", "values": ["EVALUATION", "MEETING", "CONSULTATION", "ALL"]}, "public.blocked_date_type": {"name": "blocked_date_type", "schema": "public", "values": ["HOLIDAY", "STAFF_TRAINING", "DISTRICT_CLOSURE", "MAINTENANCE", "SPECIAL_EVENT", "VACATION", "WEATHER", "EMERGENCY", "OTHER"]}, "public.case_priority": {"name": "case_priority", "schema": "public", "values": ["LOW", "MEDIUM", "HIGH", "URGENT"]}, "public.case_status": {"name": "case_status", "schema": "public", "values": ["READY_FOR_EVALUATION", "EVALUATION_IN_PROGRESS", "REPORT_IN_PROGRESS", "AWAITING_MEETING", "MEETING_COMPLETE"]}, "public.case_type": {"name": "case_type", "schema": "public", "values": ["INITIAL_EVALUATION", "TRIENNIAL_EVALUATION", "REEVALUATION", "INDEPENDENT_EVALUATION"]}, "public.case_workflow_status": {"name": "case_workflow_status", "schema": "public", "values": ["PENDING", "IN_PROGRESS", "COMPLETED", "SKIPPED"]}, "public.day_of_week": {"name": "day_of_week", "schema": "public", "values": ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"]}, "public.district_type": {"name": "district_type", "schema": "public", "values": ["ELEMENTARY_DISTRICT", "SECONDARY_DISTRICT", "UNIFIED_DISTRICT", "SUPERVISORY_UNION_ADMIN", "REGIONAL_SERVICE_AGENCY", "STATE_OPERATED_AGENCY", "FEDERAL_OPERATED_AGENCY", "CHARTER_LEA", "OTHER_EDUCATION_AGENCY", "SPECIALIZED_PUBLIC_DISTRICT"]}, "public.document_category": {"name": "document_category", "schema": "public", "values": ["ASSESSMENT", "BACKGROUND", "CONSENT_FORM", "OTHER"]}, "public.enrollment_status": {"name": "enrollment_status", "schema": "public", "values": ["ENROLLED", "WITHDRAWN", "TRANSFERRED", "GRADUATED", "SUSPENDED", "EXPELLED", "INACTIVE"]}, "public.event_status": {"name": "event_status", "schema": "public", "values": ["SCHEDULED", "CANCELLED", "COMPLETED", "RESCHEDULED", "NO_SHOW"]}, "public.event_type": {"name": "event_type", "schema": "public", "values": ["EVALUATION", "IEP_MEETING", "OBSERVATION_MEETING", "GENERAL_MEETING"]}, "public.issue_type": {"name": "issue_type", "schema": "public", "values": ["VISUAL", "FUNCTIONALITY", "PERFORMANCE", "SECURITY", "DATA", "USABILITY", "ACCESSIBILITY", "OTHER"]}, "public.status": {"name": "status", "schema": "public", "values": ["OPEN", "IN_PROGRESS", "RESOLVED", "RELEASED", "REJECTED"]}, "public.feedback_type": {"name": "feedback_type", "schema": "public", "values": ["BUG", "FEATURE_REQUEST", "GENERAL"]}, "public.gender": {"name": "gender", "schema": "public", "values": ["MALE", "FEMALE", "NON_BINARY", "PREFER_NOT_TO_SAY", "OTHER"]}, "public.iep_status": {"name": "iep_status", "schema": "public", "values": ["ACTIVE", "INACTIVE"]}, "public.index_type": {"name": "index_type", "schema": "public", "values": ["PRIMARY", "COMPOSITE", "SUPPLEMENTAL", "PROCESS_SPECIFIC", "ANCILLARY"]}, "public.invitation_status": {"name": "invitation_status", "schema": "public", "values": ["PENDING", "ACCEPTED", "REJECTED", "EXPIRED"]}, "public.notification_category_type": {"name": "notification_category_type", "schema": "public", "values": ["GENERAL", "TASKS", "EVALUATIONS", "REPORTS", "MEETINGS"]}, "public.notification_channel": {"name": "notification_channel", "schema": "public", "values": ["EMAIL", "IN_APP"]}, "public.notification_type": {"name": "notification_type", "schema": "public", "values": ["GENERAL", "SYSTEM_UPDATE", "REMINDER", "TASK_ASSIGNED", "TASK_ACCEPTED", "TASK_REJECTED", "EVALUATION_SCHEDULED", "RATING_SCALES_REMINDER", "UPCOMING_EVALUATION_REMINDER", "MEETING_SCHEDULED", "MEETING_DEADLINE_APPROACHING", "APPROVAL_NEEDED", "REPORT_READY"]}, "public.parent_relationship": {"name": "parent_relationship", "schema": "public", "values": ["MOTHER", "FATHER", "STEP_MOTHER", "STEP_FATHER", "GUARDIAN", "ADOPTIVE_MOTHER", "ADOPTIVE_FATHER", "GRANDMOTHER", "GRANDFATHER", "AUNT", "UNCLE", "FOSTER_MOTHER", "FOSTER_FATHER", "OTHER_RELATIVE", "NON_RELATIVE", "UNKNOWN"]}, "public.plan_status": {"name": "plan_status", "schema": "public", "values": ["PENDING", "ACTIVE", "CANCELLED"]}, "public.plan_type": {"name": "plan_type", "schema": "public", "values": ["IEP", "504", "BIP", "SST"]}, "public.preference_category": {"name": "preference_category", "schema": "public", "values": ["NOTIFICATIONS", "USER_MANAGEMENT", "WORKFLOW", "REPORTING", "INTEGRATIONS", "EVALUATION", "SECURITY"]}, "public.preference_type": {"name": "preference_type", "schema": "public", "values": ["BOOLEAN", "STRING", "NUMBER", "JSON"]}, "public.role": {"name": "role", "schema": "public", "values": ["SUPER_USER", "SPECIAL_ED_DIRECTOR", "SCHOOL_COORDINATOR", "SCHOOL_ADMIN", "PROCTOR", "CASE_MANAGER", "CLINICAL_DIRECTOR", "PSYCHOLOGIST", "ASSISTANT"]}, "public.school_type": {"name": "school_type", "schema": "public", "values": ["REGULAR_PUBLIC_PRIMARY", "REGULAR_PUBLIC_MIDDLE", "REGULAR_PUBLIC_HIGH", "REGULAR_PUBLIC_UNIFIED", "SPECIAL_ED_PUBLIC", "VOCATIONAL_PUBLIC", "ALTERNATIVE_PUBLIC", "REPORTABLE_PROGRAM", "PUBLIC_CHARTER", "MAGNET_PUBLIC", "VIRTUAL_PUBLIC", "DODEA_SCHOOL", "BIE_SCHOOL", "PRIVATE_CATHOLIC", "PRIVATE_OTHER_RELIGIOUS", "PRIVATE_NONSECTARIAN"]}, "public.session_status": {"name": "session_status", "schema": "public", "values": ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED", "RESCHEDULED", "INCOMPLETE"]}, "public.session_type": {"name": "session_type", "schema": "public", "values": ["INITIAL_EVALUATION", "TRIENNIAL_EVALUATION", "REEVALUATION", "INDEPENDENT_EVALUATION", "PROGRESS_MONITORING", "DIAGNOSTIC_CLARIFICATION", "COMPREHENSIVE_EVALUATION"]}, "public.subtest_type": {"name": "subtest_type", "schema": "public", "values": ["CORE", "SUPPLEMENTAL", "PROCESS_SPECIFIC", "OPTIONAL", "ANCILLARY", "RATING_SCALE"]}, "public.task_history_action": {"name": "task_history_action", "schema": "public", "values": ["CREATED", "ASSIGNED", "REASSIGNED", "STATUS_CHANGED", "PRIORITY_CHANGED", "DUE_DATE_CHANGED", "COMPLETED", "CANCELLED", "NOTES_ADDED", "REOPENED", "REJECTED"]}, "public.task_priority": {"name": "task_priority", "schema": "public", "values": ["LOW", "MEDIUM", "HIGH", "URGENT"]}, "public.task_status": {"name": "task_status", "schema": "public", "values": ["TODO", "IN_PROGRESS", "COMPLETED", "CANCELLED", "REJECTED"]}, "public.task_type": {"name": "task_type", "schema": "public", "values": ["ASSIGN_PSYCHOLOGIST", "REASSIGN_PSYCHOLOGIST", "REVIEW_DISTRICT_ASSIGNMENT", "SHIP_EVALUATION_MATERIALS", "UPDATE_AVAILABILITY", "COMPLETE_REFERRAL_FORM", "SCHEDULE_STUDENT_EVALUATIONS", "GENERATE_CALENDAR_INVITES", "CREATE_EVALUATION_PLAN", "PREPARE_RATING_SCALES", "REVIEW_AND_SEND_RATING_SCALES", "MONITOR_RATING_SCALES", "PREPARE_ASSESSMENT_MATERIALS", "PREPARE_FOR_EVALUATION", "JOIN_EVALUATION_AS_PROCTOR", "JOIN_EVALUATION_AS_PSYCHOLOGIST", "MARK_EVALUATION_COMPLETE", "COMPLETE_STUDENT_INTERVIEW", "UPLOAD_PROTOCOLS", "UPDATE_ASSESSMENT_SCORES", "GENERATE_REPORT_DRAFT", "FINALIZE_EVALUATION_REPORT", "SCORE_REPORT_QUALITY", "REVIEW_FINAL_REPORT", "MARK_REPORT_RECEIVED", "SCHEDULE_IEP_MEETING", "PREPARE_FOR_IEP_MEETING", "SEND_MEETING_INVITATIONS", "COMPLETE_IEP_MEETING"]}, "public.test_category": {"name": "test_category", "schema": "public", "values": ["COGNITIVE_ASSESSMENT", "ACADEMIC_ACHIEVEMENT", "SOCIAL_EMOTIONAL_ASSESSMENT", "NEUROPSYCHOLOGICAL_ASSESSMENT", "ADAPTIVE_BEHAVIOR"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}