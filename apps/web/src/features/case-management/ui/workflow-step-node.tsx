import { CaseWorkflowStatusEnum } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import { cn } from '@lilypad/ui/lib/utils';
import { Handle, type NodeProps, Position } from '@xyflow/react';
import { CheckCircle2, Circle, Clock, XCircle } from 'lucide-react';
import type { WorkflowStep } from '../lib/types';

const STATUS_ICONS = {
  [CaseWorkflowStatusEnum.COMPLETED]: (
    <CheckCircle2 className="size-5 text-green-600" />
  ),
  [CaseWorkflowStatusEnum.IN_PROGRESS]: (
    <Clock className="size-5 text-blue-600" />
  ),
  [CaseWorkflowStatusEnum.SKIPPED]: (
    <XCircle className="size-5 text-gray-400" />
  ),
  [CaseWorkflowStatusEnum.PENDING]: <Circle className="size-5 text-gray-400" />,
} as const;

const STATUS_COLORS = {
  [CaseWorkflowStatusEnum.COMPLETED]:
    'border-green-500 bg-green-50 dark:bg-green-950/20',
  [CaseWorkflowStatusEnum.IN_PROGRESS]:
    'border-blue-500 bg-blue-50 dark:bg-blue-950/20',
  [CaseWorkflowStatusEnum.SKIPPED]:
    'border-gray-300 bg-gray-50 dark:bg-gray-950/20',
  [CaseWorkflowStatusEnum.PENDING]:
    'border-gray-300 bg-white dark:bg-gray-950/10',
} as const;

export function WorkflowStepNode({ data }: NodeProps) {
  const { step } = data as { step: WorkflowStep };

  const statusIcon =
    STATUS_ICONS[step.status as CaseWorkflowStatusEnum] ||
    STATUS_ICONS[CaseWorkflowStatusEnum.PENDING];
  const statusColor =
    STATUS_COLORS[step.status as CaseWorkflowStatusEnum] ||
    STATUS_COLORS[CaseWorkflowStatusEnum.PENDING];

  return (
    <>
      <Handle position={Position.Top} type="target" />
      <div
        className={cn(
          '!bg-background rounded-lg border-2 p-4 shadow-sm transition-all hover:shadow-md',
          statusColor
        )}
      >
        <div className="mb-2 flex items-center justify-between">
          <span className="font-semibold text-sm">Step {step.stepNumber}</span>
          {statusIcon}
        </div>

        <h3 className="mb-1 font-medium text-sm leading-tight">{step.name}</h3>

        <If condition={step.description}>
          <p className="mb-2 text-muted-foreground text-xs leading-tight">
            {step.description}
          </p>
        </If>

        <div className="flex flex-col gap-1">
          <If condition={step.estimatedDays}>
            <span className="text-muted-foreground text-xs">
              Est. {step.estimatedDays} day{step.estimatedDays !== 1 ? 's' : ''}
            </span>
          </If>

          <If condition={step.isOptional}>
            <Badge className="w-fit text-xs" variant="secondary">
              Optional
            </Badge>
          </If>
        </div>
      </div>
      <Handle position={Position.Bottom} type="source" />
    </>
  );
}
