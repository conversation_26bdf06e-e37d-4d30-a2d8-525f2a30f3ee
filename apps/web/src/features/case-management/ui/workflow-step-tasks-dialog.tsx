
'use client';

import type { TRPCClientError } from '@lilypad/api/client';
import { useTRPC } from '@lilypad/api/client';
import { CaseWorkflowStatusEnum } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { If } from '@lilypad/ui/components/if';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { toast } from '@lilypad/ui/components/sonner';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { useMutation, useQuery, } from '@tanstack/react-query';
import {
  AlertCircle,
  ListTodoIcon,
} from 'lucide-react';
import { formatTasksToTableRow } from '@/entities/tasks/model/schema';
import { TaskCard } from '@/entities/tasks/ui/task-card';
import { CaseWorkflowStepStatusBadge } from '@/shared/ui/cases';
import type { WorkflowStep } from '../lib/types';
import { TemplateTaskItem } from './template-task-item';

interface WorkflowStepTasksDialogProps {
  step: WorkflowStep | null;
  caseId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function WorkflowStepTasksDialog({
  step,
  caseId,
  open,
  onOpenChange,
}: WorkflowStepTasksDialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp);
  const trpc = useTRPC();

  const { data: canCompleteData } = useQuery(
    trpc.workflows.canCompleteStepBasedOnTasks.queryOptions({
      stepId: step?.id as string,
      caseId,
    },
      {
        enabled: !!step?.id && step?.status !== CaseWorkflowStatusEnum.COMPLETED,
      })
  );

  const completeStepMutation = useMutation(
    trpc.workflows.completeWorkflowStep.mutationOptions({
      onSuccess: () => {
        onOpenChange(false);
      },
      onError: (error: TRPCClientError) => {
        const message = error.message || 'Failed to complete workflow step';
        toast.error(message);
      },
    })
  );

  const handleCompleteStep = () => {
    if (!(caseId && step?.id)) {
      return;
    }

    completeStepMutation.mutate({
      caseId,
      stepId: step.id,
    });
  };

  const renderContent = () => {
    if (!step) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 font-medium text-lg">No step selected</h3>
          <p className="text-muted-foreground text-sm">
            Please select a workflow step to view its tasks.
          </p>
        </div>
      );
    }

    return <DialogContentComponent step={step} caseId={caseId} />;
  };

  const renderButtons = () => {
    const isStepCompleted = step?.status === CaseWorkflowStatusEnum.COMPLETED;
    const canComplete = canCompleteData?.canComplete ?? false;
    const isLoading = completeStepMutation.isPending;

    return (
      <div
        className={cn(
          'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md',
          mdUp && 'rounded-b-md',
          isStepCompleted && 'md:justify-end'
        )}
      >
        <Button
          onClick={() => onOpenChange(false)}
          size="sm"
          type="button"
          variant="outline"
          className={cn('w-1/2 md:w-auto', isStepCompleted && 'w-full')}
        >
          Close
        </Button>

        <If condition={!isStepCompleted}>
          <Button
            onClick={handleCompleteStep}
            size="sm"
            type="button"
            disabled={!canComplete || isLoading}
            className='w-1/2 md:w-auto'
          >
            {isLoading ? 'Completing...' : 'Complete Step'}
          </Button>
        </If>
      </div>
    );
  };

  return (
    <>
      {mdUp ? (
        <Dialog onOpenChange={onOpenChange} open={open}>
          <DialogContent className="flex max-h-[85vh] max-w-4xl flex-col gap-0 p-0">
            <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
              <DialogTitle className="flex items-center gap-2 font-semibold text-lg">
                {step?.name}
                <If condition={step?.status}>
                  {(stepStatus) => (
                    <CaseWorkflowStepStatusBadge
                      size="sm"
                      status={stepStatus}
                    />
                  )}
                </If>
              </DialogTitle>
              <DialogDescription className="text-muted-foreground text-sm">
                {step?.description}
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 px-6 py-4 scrollarea overflow-y-auto">
              {renderContent()}
            </div>
            {renderButtons()}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer onOpenChange={onOpenChange} open={open}>
          <DrawerContent className="flex max-h-[90vh] flex-col">
            <DrawerHeader className="flex-shrink-0 border-b text-left">
              <DrawerTitle className="font-semibold text-lg">
                {step?.name}
              </DrawerTitle>
              <DrawerDescription className="text-muted-foreground text-sm">
                {step?.description}
              </DrawerDescription>
            </DrawerHeader>
            <div className="flex-1 px-4 py-4 scrollarea overflow-y-auto">
              {renderContent()}
            </div>
            {renderButtons()}
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
}

function DialogContentComponent({
  step,
  caseId
}: {
  step: WorkflowStep;
  caseId?: string;
}) {
  const trpc = useTRPC();

  const { data: stepTasks, isError, isLoading } = useQuery(
    trpc.workflows.getWorkflowStepTasks.queryOptions({
      workflowStepId: step.id,
      caseId,
    })
  );

  if (isLoading) {
    return <DialogContentSkeleton />;
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="mb-4 size-12 text-red-500" />
        <h3 className="mb-2 font-medium text-lg">Error loading tasks</h3>
        <p className="text-muted-foreground text-sm">
          Unable to load tasks for this workflow step. Please try again.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="flex items-center gap-2 font-semibold text-sm">
          <span>Tasks</span>
          <Badge size="sm" variant="secondary">
            {stepTasks?.length || 0}
          </Badge>
        </h4>
      </div>

      <If condition={stepTasks?.length === 0}>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <ListTodoIcon className="mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 font-medium text-lg">No tasks configured</h3>
          <p className="max-w-sm text-muted-foreground text-sm">
            There are no template tasks configured for this workflow step.
          </p>
        </div>
      </If>

      <If condition={stepTasks}>
        {(tasks) => (
          <div className="space-y-6">
            <If condition={tasks.some(t => !t.hasActualTask)}>
              <div className="space-y-3">
                <h5 className="font-medium text-muted-foreground text-sm uppercase tracking-wide">
                  Template Tasks
                </h5>
                <div className="space-y-3">
                  {tasks
                    .filter(stepTask => !stepTask.hasActualTask)
                    .sort((a, b) => a.template.orderIndex - b.template.orderIndex)
                    .map((stepTask) => (
                      <TemplateTaskItem
                        key={stepTask.template.id}
                        template={stepTask.template}
                      />
                    ))}
                </div>
              </div>
            </If>

            <If condition={tasks.some(t => t.hasActualTask && t.actualTask)}>
              <div className="space-y-3">
                <h5 className="font-medium text-muted-foreground text-sm uppercase tracking-wide">
                  Active Tasks
                </h5>
                <div className="space-y-3">
                  {(() => {
                    const actualTasks = tasks
                      .filter(stepTask => stepTask.hasActualTask && stepTask.actualTask)
                      .map(stepTask => stepTask.actualTask)
                      .filter((task): task is NonNullable<typeof task> => task !== null);
                    const formattedTasks = formatTasksToTableRow(actualTasks);

                    return formattedTasks.map((task) => (
                      <TaskCard
                        key={task.id}
                        task={task}
                        showAssigneeAvatar={true}
                      />
                    ));
                  })()}
                </div>
              </div>
            </If>
          </div>
        )}
      </If>
    </div>
  );
}

function DialogContentSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-12" />
          <Skeleton className="h-5 w-8 rounded-full" />
        </div>
      </div>
      <div className="space-y-3">
        {Array.from({ length: 3 }, (_, index) => (
          <div key={`skeleton-task-item-${index}`} className="rounded-xl border p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <Skeleton className="size-8 rounded-md" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-6 w-16 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
            </div>
            <div className="mt-3 flex items-center gap-4">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}