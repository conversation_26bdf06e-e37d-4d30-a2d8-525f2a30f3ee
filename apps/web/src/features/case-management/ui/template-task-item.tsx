'use client';

import { type RoleEnum, TaskTypeEnumMap } from '@lilypad/db/enums';
import type { TaskWithRelations } from '@lilypad/db/repository/types/tasks';
import type { WorkflowStepTask } from '@lilypad/db/schema/types';
import { Badge } from '@lilypad/ui/components/badge';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import { RolesBadge } from '@lilypad/ui/components/roles-badge';
import { cn } from '@lilypad/ui/lib/utils';
import {
  AlertCircle,
  Clock,
  FileText,
  User,
} from 'lucide-react';
import { getTaskConfig } from '@/entities/tasks/ui/task-config';

interface TemplateTaskItemProps {
  template: WorkflowStepTask;
  actualTask?: TaskWithRelations | null;
  hasActualTask?: boolean;
}

export function TemplateTaskItem({
  template,
}: TemplateTaskItemProps) {
  const taskDetails = TaskTypeEnumMap[template.taskType];
  const taskConfig = getTaskConfig(template.taskType);

  return (
    <Card
      className={cn(
        'transition-all duration-200 border-2 border-dashed border-muted-foreground/20 bg-muted/10 p-0 relative overflow-hidden shadow-none'
      )}
    >
      <CardContent className="p-4">
        {/* Template indicator */}
        <div className="absolute top-2 right-2">
          <Badge
            variant="secondary"
            size="sm"
            className="bg-muted/60 text-muted-foreground border-muted-foreground/30"
          >
            <FileText className="size-3 mr-1" />
            Template
          </Badge>
        </div>

        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start gap-3 pr-20">
            <div className={cn(
              "flex-shrink-0 rounded-lg border-2 border-dashed p-2",
              "border-muted-foreground/20 bg-muted/20"
            )}>
              <If condition={taskConfig.icon}>
                {(Icon) => (
                  <Icon className={cn("size-4 text-muted-foreground")} />
                )}
              </If>
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm text-muted-foreground leading-tight">
                {taskDetails.name}
              </h4>
              <p className="text-xs text-muted-foreground/70 mt-1 line-clamp-2">
                {taskDetails.description}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <If condition={template.isRequired}>
              <Badge
                variant="outline"
                size="sm"
                className="border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-700 dark:bg-amber-900/20 dark:text-amber-300"
              >
                <AlertCircle className="size-3 mr-1" />
                Required
              </Badge>
            </If>

            <If condition={template.estimatedTimeline}>
              <Badge
                variant="outline"
                size="sm"
                className="border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-300"
              >
                <Clock className="size-3 mr-1" />
                {template.estimatedTimeline ?? 'Not Specified'}
              </Badge>
            </If>

            <If condition={template.autoAssignRole}>
              <RolesBadge role={template.autoAssignRole as RoleEnum} />
            </If>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}