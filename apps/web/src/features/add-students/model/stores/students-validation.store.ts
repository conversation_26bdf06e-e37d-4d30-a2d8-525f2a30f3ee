import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { StudentRowData } from '../schema';

// biome-ignore lint/suspicious/noExplicitAny: It's fine
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeout) { clearTimeout(timeout); }
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface ValidationState {
  errors: Map<string, Record<string, string>>;
  validationQueue: Set<string>;
}

interface ValidationActions {
  validateStudent: (student: StudentRowData) => void;
  validateStudentImmediate: (student: StudentRowData) => void;
  validateAllStudents: (students: StudentRowData[]) => boolean;
  clearErrors: (studentId: string) => void;
  clearAllErrors: () => void;
  getErrors: (studentId: string) => Record<string, string> | undefined;
  hasErrors: (studentId: string) => boolean;
}

type ValidationStore = ValidationState & ValidationActions;

export const useValidationStore = create<ValidationStore>()(
  immer((set, get) => {
    // Internal validation logic
    const performValidation = (student: StudentRowData) => {
      const errors: Record<string, string> = {};

      // Required field validation
      if (!student.studentIdNumber) { errors.studentIdNumber = 'Required'; }
      if (!student.firstName) { errors.firstName = 'Required'; }
      if (!student.lastName) { errors.lastName = 'Required'; }
      if (!student.preferredName) { errors.preferredName = 'Required'; }
      if (!student.dateOfBirth) { errors.dateOfBirth = 'Required'; }
      if (!student.dateOfConsent) { errors.dateOfConsent = 'Required'; }
      if (!student.grade) { errors.grade = 'Required'; }
      if (!student.gender) { errors.gender = 'Required'; }
      if (!student.primarySchoolId) { errors.primarySchoolId = 'Required'; }
      if (student.languageIds.length === 0) {
        errors.languageIds = 'At least one language required';
      }
      if (student.languageIds.length > 0 && !student.primaryLanguageId) {
        errors.primaryLanguageId = 'Primary language must be selected';
      }
      if (
        student.primaryLanguageId &&
        !student.languageIds.includes(student.primaryLanguageId)
      ) {
        errors.primaryLanguageId =
          'Primary language must be one of the selected languages';
      }
      if (student.parents.length === 0) {
        errors.parents = 'At least one parent/guardian required';
      }

      // Out of district and private school validation
      if (
        student.outOfDistrict &&
        (!student.privateSchool || student.privateSchool.trim() === '')
      ) {
        errors.privateSchool = 'Private school name is required';
      }

      set((state) => {
        if (Object.keys(errors).length > 0) {
          state.errors.set(student.id, errors);
        } else {
          state.errors.delete(student.id);
        }
      });
    };

    // Create debounced version
    const debouncedValidation = debounce(performValidation, 300);

    return {
      errors: new Map(),
      validationQueue: new Set(),

      validateStudent: debouncedValidation,

      validateStudentImmediate: performValidation,

      validateAllStudents: (students) => {
        let allValid = true;

        set((state) => {
          state.errors.clear();

          for (const student of students) {
            const errors: Record<string, string> = {};

            // Same validation logic as validateStudent
            if (!student.studentIdNumber) { errors.studentIdNumber = 'Required'; }
            if (!student.firstName) { errors.firstName = 'Required'; }
            if (!student.lastName) { errors.lastName = 'Required'; }
            if (!student.preferredName) { errors.preferredName = 'Required'; }
            if (!student.dateOfBirth) { errors.dateOfBirth = 'Required'; }
            if (!student.dateOfConsent) { errors.dateOfConsent = 'Required'; }
            if (!student.grade) { errors.grade = 'Required'; }
            if (!student.gender) { errors.gender = 'Required'; }
            if (!student.primarySchoolId) { errors.primarySchoolId = 'Required'; }
            if (student.languageIds.length === 0) {
              errors.languageIds = 'At least one language required';
            }
            if (student.languageIds.length > 0 && !student.primaryLanguageId) {
              errors.primaryLanguageId = 'Primary language must be selected';
            }
            if (
              student.primaryLanguageId &&
              !student.languageIds.includes(student.primaryLanguageId)
            ) {
              errors.primaryLanguageId =
                'Primary language must be one of the selected languages';
            }
            if (student.parents.length === 0) {
              errors.parents = 'At least one parent/guardian required';
            }

            // Out of district and private school validation
            if (
              student.outOfDistrict &&
              (!student.privateSchool || student.privateSchool.trim() === '')
            ) {
              errors.privateSchool = 'Private school name is required';
            }

            if (Object.keys(errors).length > 0) {
              state.errors.set(student.id, errors);
              allValid = false;
            }
          }
        });

        return allValid;
      },

      clearErrors: (studentId) =>
        set((state) => {
          state.errors.delete(studentId);
        }),

      clearAllErrors: () =>
        set((state) => {
          state.errors.clear();
        }),

      getErrors: (studentId) => get().errors.get(studentId),
      hasErrors: (studentId) => get().errors.has(studentId),
    };
  })
);
