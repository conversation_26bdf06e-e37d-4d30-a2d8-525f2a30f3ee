import type { ParentSchema } from '@lilypad/api/schemas/students';
import {
  GenderEnum,
  ParentRelationshipEnum,
  SchoolGradeEnum,
} from '@lilypad/db/enums';
import { nanoid } from 'nanoid';
import type { CSVRowData, StudentRowData } from '../schema';

const DATE_REGEX = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
const NAME_REGEX = /\s+/;

interface MappingResult {
  studentData: StudentRowData;
  errors: string[];
  warnings: string[];
}

interface LookupMaps {
  schools: Map<string, string>; // name -> id
  languages: Map<string, string>; // name -> id
  grades: Map<string, SchoolGradeEnum>; // display name -> enum
}

export class CSVDataMapper {
  private lookupMaps: LookupMaps;

  constructor(
    schools: Array<{ id: string; name: string }>,
    languages: Array<{ id: string; name: string; emoji?: string }>
  ) {
    this.lookupMaps = this.buildLookupMaps(schools, languages);
  }

  private buildLookupMaps(
    schools: Array<{ id: string; name: string }>,
    languages: Array<{ id: string; name: string; emoji?: string }>
  ): LookupMaps {
    // Build school lookup map
    const schoolsMap = new Map<string, string>();
    for (const school of schools) {
      schoolsMap.set(school.name.toLowerCase().trim(), school.id);
    }

    // Build language lookup map
    const languagesMap = new Map<string, string>();
    for (const language of languages) {
      languagesMap.set(language.name.toLowerCase().trim(), language.id);
    }

    // Build grade lookup map
    const gradesMap = new Map<string, SchoolGradeEnum>([
      ['preschool', SchoolGradeEnum.PRESCHOOL],
      ['pre-k', SchoolGradeEnum.PRESCHOOL],
      ['pk', SchoolGradeEnum.PRESCHOOL],
      ['kindergarten', SchoolGradeEnum.KINDERGARTEN],
      ['k', SchoolGradeEnum.KINDERGARTEN],
      ['1st', SchoolGradeEnum.FIRST_GRADE],
      ['1st grade', SchoolGradeEnum.FIRST_GRADE],
      ['1', SchoolGradeEnum.FIRST_GRADE],
      ['2nd', SchoolGradeEnum.SECOND_GRADE],
      ['2nd grade', SchoolGradeEnum.SECOND_GRADE],
      ['2', SchoolGradeEnum.SECOND_GRADE],
      ['3rd', SchoolGradeEnum.THIRD_GRADE],
      ['3rd grade', SchoolGradeEnum.THIRD_GRADE],
      ['3', SchoolGradeEnum.THIRD_GRADE],
      ['4th', SchoolGradeEnum.FOURTH_GRADE],
      ['4th grade', SchoolGradeEnum.FOURTH_GRADE],
      ['4', SchoolGradeEnum.FOURTH_GRADE],
      ['5th', SchoolGradeEnum.FIFTH_GRADE],
      ['5th grade', SchoolGradeEnum.FIFTH_GRADE],
      ['5', SchoolGradeEnum.FIFTH_GRADE],
      ['6th', SchoolGradeEnum.SIXTH_GRADE],
      ['6th grade', SchoolGradeEnum.SIXTH_GRADE],
      ['6', SchoolGradeEnum.SIXTH_GRADE],
      ['7th', SchoolGradeEnum.SEVENTH_GRADE],
      ['7th grade', SchoolGradeEnum.SEVENTH_GRADE],
      ['7', SchoolGradeEnum.SEVENTH_GRADE],
      ['8th', SchoolGradeEnum.EIGHTH_GRADE],
      ['8th grade', SchoolGradeEnum.EIGHTH_GRADE],
      ['8', SchoolGradeEnum.EIGHTH_GRADE],
      ['9th', SchoolGradeEnum.NINTH_GRADE],
      ['9th grade', SchoolGradeEnum.NINTH_GRADE],
      ['9', SchoolGradeEnum.NINTH_GRADE],
      ['10th', SchoolGradeEnum.TENTH_GRADE],
      ['10th grade', SchoolGradeEnum.TENTH_GRADE],
      ['10', SchoolGradeEnum.TENTH_GRADE],
      ['11th', SchoolGradeEnum.ELEVENTH_GRADE],
      ['11th grade', SchoolGradeEnum.ELEVENTH_GRADE],
      ['11', SchoolGradeEnum.ELEVENTH_GRADE],
      ['12th', SchoolGradeEnum.TWELFTH_GRADE],
      ['12th grade', SchoolGradeEnum.TWELFTH_GRADE],
      ['12', SchoolGradeEnum.TWELFTH_GRADE],
      ['ungraded', SchoolGradeEnum.UNGRADED],
      ['post graduate', SchoolGradeEnum.POST_GRADUATE],
      ['pg', SchoolGradeEnum.POST_GRADUATE],
    ]);

    return {
      schools: schoolsMap,
      languages: languagesMap,
      grades: gradesMap,
    };
  }

  mapRow(csvRow: CSVRowData): MappingResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const dateOfBirth = this.parseDate(csvRow['Date of Birth']);

    if (!dateOfBirth) {
      errors.push('Invalid date of birth format');
    }

    const grade = this.parseGrade(csvRow.Grade);

    if (!grade) {
      errors.push(`Unknown grade: ${csvRow.Grade}`);
    }

    const schoolId = this.findSchool(csvRow.School);

    if (!schoolId) {
      errors.push(`School not found: ${csvRow.School}`);
    }

    const languageResult = this.parseLanguages(csvRow.Languages);

    if (languageResult.errors.length > 0) {
      errors.push(...languageResult.errors);
    }

    if (languageResult.warnings.length > 0) {
      warnings.push(...languageResult.warnings);
    }

    const parent = this.createParentFromGuardian(
      csvRow['Guardian Name'],
      csvRow['Guardian Email'],
      csvRow['Guardian Phone']
    );

    const studentData: StudentRowData = {
      id: nanoid(),
      studentIdNumber: csvRow['Student ID'].trim(),
      firstName: csvRow['First Name'].trim(),
      middleName: csvRow['Middle Name']?.trim() || '',
      lastName: csvRow['Last Name'].trim(),
      preferredName: csvRow['Preferred Name'].trim(),
      dateOfBirth: dateOfBirth || new Date(),
      dateOfConsent: new Date(),
      grade: grade || SchoolGradeEnum.KINDERGARTEN,
      gender: GenderEnum.PREFER_NOT_TO_SAY,
      primarySchoolId: schoolId || '',
      outOfDistrict: false,
      privateSchool: '',
      languageIds: languageResult.languageIds,
      primaryLanguageId: languageResult.primaryLanguageId,
      parents: [parent],
      documents: [],
      hasErrors: errors.length > 0,
      errors: {},
    };

    return {
      studentData,
      errors,
      warnings,
    };
  }

  private parseDate(dateStr: string): Date | null {
    // Handle MM/DD/YYYY format
    const match = dateStr.match(DATE_REGEX);
    if (!match) {
      return null;
    }

    const month = Number.parseInt(match[1], 10);
    const day = Number.parseInt(match[2], 10);
    const year = Number.parseInt(match[3], 10);

    // Validate ranges
    if (month < 1 || month > 12 || day < 1 || day > 31) {
      return null;
    }

    const date = new Date(year, month - 1, day);

    // Check if the date is valid and matches what we created
    if (
      date.getFullYear() !== year ||
      date.getMonth() !== month - 1 ||
      date.getDate() !== day
    ) {
      return null;
    }

    return date;
  }

  private parseGrade(gradeStr: string): SchoolGradeEnum | null {
    const normalized = gradeStr.toLowerCase().trim();
    return this.lookupMaps.grades.get(normalized) || null;
  }

  private findSchool(schoolName: string): string | null {
    const normalized = schoolName.toLowerCase().trim();
    return this.lookupMaps.schools.get(normalized) || null;
  }

  private parseLanguages(languagesStr: string): {
    languageIds: string[];
    primaryLanguageId: string | null;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const languageIds: string[] = [];

    // Split by comma and clean up
    const languageNames = languagesStr
      .split(',')
      .map((lang) => lang.trim())
      .filter((lang) => lang.length > 0);

    if (languageNames.length === 0) {
      errors.push('No languages specified');
      return { languageIds: [], primaryLanguageId: null, errors, warnings };
    }

    for (const langName of languageNames) {
      const normalized = langName.toLowerCase().trim();
      const languageId = this.lookupMaps.languages.get(normalized);

      if (languageId) {
        languageIds.push(languageId);
      } else {
        warnings.push(`Language not found: ${langName}`);
      }
    }

    if (languageIds.length === 0) {
      errors.push('No valid languages found');
      return { languageIds: [], primaryLanguageId: null, errors, warnings };
    }

    // Set first language as primary
    const primaryLanguageId = languageIds[0];

    return {
      languageIds,
      primaryLanguageId,
      errors,
      warnings,
    };
  }

  private createParentFromGuardian(
    name: string,
    email: string,
    phone: string
  ): ParentSchema {
    // Parse name into first and last name
    const nameParts = name.trim().split(NAME_REGEX);
    const firstName = nameParts[0] || '';
    const lastName = nameParts.at(-1) || '';
    const middleName =
      nameParts.length > 2 ? nameParts.slice(1, -1).join(' ') : undefined;

    return {
      firstName,
      middleName,
      lastName,
      relationshipType: ParentRelationshipEnum.UNKNOWN,
      primaryEmail: email.trim() || undefined,
      secondaryEmail: undefined,
      primaryPhone: phone.trim() || undefined,
      secondaryPhone: undefined,
      isPrimaryContact: true,
      hasPickupAuthorization: true,
    };
  }
}
