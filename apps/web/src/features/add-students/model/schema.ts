import type {
  StudentSchema,
} from '@lilypad/api/schemas/students';
import { GenderEnum, SchoolGradeEnum } from '@lilypad/db/enums';
import { fileToSerializable } from '@lilypad/shared/lib/file-utils';
import { z } from 'zod';

// Create a schema that matches the table structure
export const studentRowSchema = z.object({
  id: z.string(),
  studentIdNumber: z.string().min(1, 'Student ID is required'),
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  preferredName: z.string().optional(),
  dateOfBirth: z.date({
    required_error: 'Date of birth is required',
  }),
  dateOfConsent: z.date({
    required_error: 'Date of consent is required',
  }),
  grade: z.nativeEnum(SchoolGradeEnum, {
    required_error: 'Grade is required',
  }),
  gender: z.nativeEnum(GenderEnum, {
    required_error: 'Gender is required',
  }),
  primarySchoolId: z.string().min(1, 'School is required'),
  privateSchool: z.string().optional(),
  outOfDistrict: z.boolean().default(false),
  languageIds: z
    .array(z.string())
    .min(1, 'At least one language is required'),
  primaryLanguageId: z.string().nullable().default(null),
  parents: z.array(z.any()).min(1, 'At least one parent/guardian is required'),
  documents: z.array(z.any()).default([]),
  hasErrors: z.boolean().default(false),
  errors: z.record(z.string()).default({}),
});

export type StudentRowData = z.infer<typeof studentRowSchema>;

export const bulkStudentSaveSchema = z.object({
  students: z
    .array(studentRowSchema)
    .min(1, 'At least one student is required'),
});

export const csvRowSchema = z.object({
  'Student ID': z.string().min(1, 'Student ID is required'),
  'First Name': z.string().min(1, 'First name is required'),
  'Middle Name': z.string().optional(),
  'Last Name': z.string().min(1, 'Last name is required'),
  'Preferred Name': z.string().min(1, 'Preferred name is required'),
  'Date of Birth': z
    .string()
    .regex(
      /^\d{1,2}\/\d{1,2}\/\d{4}$/,
      'Invalid date format (MM/DD/YYYY expected)'
    ),
  Grade: z.string().min(1, 'Grade is required'),
  School: z.string().min(1, 'School is required'),
  Languages: z.string().min(1, 'At least one language is required'),
  'Guardian Name': z.string().min(1, 'Guardian name is required'),
  'Guardian Email': z.string().email('Invalid email format'),
  'Guardian Phone': z
    .string()
    .min(10, 'Phone number must be at least 10 digits'),
});

export type CSVRowData = z.infer<typeof csvRowSchema>;

export const csvImportResultSchema = z.object({
  success: z.boolean(),
  data: z.array(studentRowSchema),
  errors: z.array(
    z.object({
      row: z.number(),
      field: z.string().optional(),
      message: z.string(),
      severity: z.enum(['error', 'warning']),
    })
  ),
  warnings: z.array(
    z.object({
      row: z.number(),
      field: z.string().optional(),
      message: z.string(),
      severity: z.enum(['error', 'warning']),
    })
  ),
  stats: z.object({
    totalRows: z.number(),
    validRows: z.number(),
    invalidRows: z.number(),
    processingTime: z.number(),
  }),
});

export type BulkStudentSaveData = z.infer<typeof bulkStudentSaveSchema>;
export type CSVImportResult = z.infer<typeof csvImportResultSchema>;
export type CSVImportError = CSVImportResult['errors'][0];
export type CSVImportWarning = CSVImportResult['warnings'][0];

export async function convertStudentRowForSave(
  student: StudentRowData
): Promise<StudentSchema> {
  const convertedDocuments = await Promise.all(
    student.documents.map(async (doc) => ({
      ...doc,
      file:
        doc.file instanceof File
          ? await fileToSerializable(doc.file)
          : doc.file,
    }))
  );

  return {
    ...student,
    documents: convertedDocuments,
  };
}
