'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import type { ParentSchema } from '@lilypad/api/schemas/students';
import { parentSchema } from '@lilypad/api/schemas/students';
import { ParentRelationshipEnum } from '@lilypad/db/enums';
import { normalizePhone, validatePhone } from '@lilypad/shared/phone';
import { Button } from '@lilypad/ui/components/button';
import { Form } from '@lilypad/ui/components/form';
import { toast } from '@lilypad/ui/components/sonner';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { ParentFormFields } from '@/shared/ui/students/parents/parent-form-fields';

const emailSchema = z.string().email();

interface ParentFormProps {
  onSubmit: (data: ParentSchema) => void;
  editingParent?: ParentSchema | null;
  isEditing: boolean;
  existingParentsCount?: number;
}

const defaultValues: ParentSchema = {
  firstName: '',
  middleName: '',
  lastName: '',
  relationshipType: ParentRelationshipEnum.MOTHER,
  primaryEmail: '',
  secondaryEmail: '',
  primaryPhone: '',
  secondaryPhone: '',
  isPrimaryContact: false,
  hasPickupAuthorization: false,
};

export function ParentForm({
  onSubmit,
  editingParent,
  isEditing,
  existingParentsCount = 0,
}: ParentFormProps) {
  const form = useForm<ParentSchema>({
    resolver: zodResolver(parentSchema),
    defaultValues,
  });

  const [formData, setFormData] = React.useState<ParentSchema>(defaultValues);

  // Reset form when editing parent changes
  React.useEffect(() => {
    if (editingParent) {
      const resetData = {
        firstName: editingParent.firstName || '',
        middleName: editingParent.middleName || '',
        lastName: editingParent.lastName || '',
        relationshipType:
          editingParent.relationshipType || ParentRelationshipEnum.MOTHER,
        primaryEmail: editingParent.primaryEmail || '',
        secondaryEmail: editingParent.secondaryEmail || '',
        primaryPhone: editingParent.primaryPhone || '',
        secondaryPhone: editingParent.secondaryPhone || '',
        isPrimaryContact: editingParent.isPrimaryContact,
        hasPickupAuthorization: editingParent.hasPickupAuthorization ?? true,
      };
      form.reset(resetData);
      setFormData(resetData);
    } else {
      form.reset(defaultValues);
      setFormData(defaultValues);
    }
  }, [editingParent, form]);

  // Validation function similar to add-student-form.tsx
  const validateParentData = React.useCallback(() => {
    const hasFirstName = formData.firstName?.trim();
    const hasLastName = formData.lastName?.trim();
    const hasPrimaryEmail = formData.primaryEmail?.trim();
    const hasSecondaryEmail = formData.secondaryEmail?.trim();
    const hasPrimaryPhone = formData.primaryPhone?.trim();
    const hasSecondaryPhone = formData.secondaryPhone?.trim();

    if (!hasFirstName) { return 'First name is required'; }
    if (!hasLastName) { return 'Last name is required'; }
    if (!formData.relationshipType) { return 'Relationship type is required'; }

    // Either primary email or primary phone is required
    if (!(hasPrimaryEmail || hasPrimaryPhone)) {
      return 'Either primary email or primary phone is required';
    }

    // Validate email formats
    if (hasPrimaryEmail && !emailSchema.safeParse(hasPrimaryEmail).success) {
      return 'Invalid primary email format';
    }

    if (hasSecondaryEmail && !emailSchema.safeParse(hasSecondaryEmail).success) {
      return 'Invalid secondary email format';
    }

    // Validate phone formats
    if (hasPrimaryPhone && !validatePhone(normalizePhone(hasPrimaryPhone))) {
      return 'Invalid primary phone number format';
    }

    if (hasSecondaryPhone && !validatePhone(normalizePhone(hasSecondaryPhone))) {
      return 'Invalid secondary phone number format';
    }

    return null;
  }, [formData]);

  const handleFieldChange = (field: keyof ParentSchema, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    form.setValue(field, value);
  };

  const handleSubmit = () => {
    const validationError = validateParentData();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    // Transform data similar to add-student-form.tsx
    const transformedData: ParentSchema = {
      firstName: formData.firstName as string,
      middleName: formData.middleName || undefined,
      lastName: formData.lastName as string,
      relationshipType: formData.relationshipType as ParentRelationshipEnum,
      primaryEmail: formData.primaryEmail || undefined,
      primaryPhone: formData.primaryPhone || undefined,
      secondaryEmail: formData.secondaryEmail || undefined,
      secondaryPhone: formData.secondaryPhone || undefined,
      isPrimaryContact:
        existingParentsCount === 0 ? true : Boolean(formData.isPrimaryContact),
      hasPickupAuthorization: Boolean(formData.hasPickupAuthorization),
    };

    onSubmit(transformedData);
    form.reset(defaultValues);
    setFormData(defaultValues);
  };

  const isFormValid = !validateParentData();

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-sm">
        {isEditing ? 'Edit Parent/Guardian' : 'Add Parent/Guardian'}
      </h3>
      <Form {...form}>
        <div className="space-y-3">
          <ParentFormFields
            data={formData}
            firstParent={existingParentsCount === 0 && !isEditing}
            onChange={handleFieldChange}
          />

          <Button 
            className="mt-4 w-full" 
            disabled={!isFormValid}
            onClick={handleSubmit}
            size="sm" 
            type="button"
          >
            {isEditing ? 'Update Parent/Guardian' : 'Add Parent/Guardian'}
          </Button>
        </div>
      </Form>
    </div>
  );
}
