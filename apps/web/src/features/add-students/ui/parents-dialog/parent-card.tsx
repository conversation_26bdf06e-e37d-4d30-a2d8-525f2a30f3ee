'use client';

import type { ParentSchema } from '@lilypad/api/schemas/students';
import { displayPhoneNumber } from '@lilypad/shared/phone';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import { Label } from '@lilypad/ui/components/label';
import {
  EditIcon,
  MailIcon,
  PhoneIcon,
  Trash2Icon,
  UserIcon,
} from 'lucide-react';
import React from 'react';
import { ParentRelationshipBadge } from '@/shared/ui/students/parents/parent-relationship-badge';

interface ParentCardProps {
  parent: ParentSchema;
  index: number;
  onEdit: (index: number) => void;
  onDelete: (index: number) => void;
}

export const ParentCard = React.memo(function ParentCardComponent({
  parent,
  index,
  onEdit,
  onDelete,
}: ParentCardProps) {
  const handleEdit = React.useCallback(() => {
    onEdit(index);
  }, [index, onEdit]);

  const handleDelete = React.useCallback(() => {
    onDelete(index);
  }, [index, onDelete]);

  return (
    <Card className="relative gap-0 px-0 py-4 shadow-none">
      <CardHeader className="px-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <UserIcon className="size-4 text-muted-foreground" />
            <CardTitle className="text-base">
              {parent.firstName} {parent.middleName} {parent.lastName}
            </CardTitle>
            <ParentRelationshipBadge relationship={parent.relationshipType} />
            <If condition={parent.isPrimaryContact}>
              <Badge
                className="gap-1 whitespace-nowrap border-primary bg-primary/10 text-primary"
                variant="outline"
              >
                Primary Contact
              </Badge>
            </If>
            <If condition={parent.hasPickupAuthorization}>
              <Badge className="text-xs" variant="secondary">
                Pickup Authorized
              </Badge>
            </If>
          </div>
          <div className="flex gap-1">
            <Button
              className="size-8 p-0"
              onClick={handleEdit}
              size="sm"
              type="button"
              variant="ghost"
            >
              <EditIcon className="size-4" />
            </Button>
            <Button
              className="size-8 p-0"
              onClick={handleDelete}
              size="sm"
              type="button"
              variant="cancel"
            >
              <Trash2Icon className="size-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="grid grid-cols-2 gap-2 px-4 text-sm">
        <If condition={parent.primaryEmail}>
          <div className="flex items-center gap-2 text-muted-foreground">
            <MailIcon className="size-3" />
            <Label className="whitespace-nowrap">Primary Email</Label>
            <span className="text-foreground">{parent.primaryEmail}</span>
          </div>
        </If>
        <If condition={parent.primaryPhone}>
          <div className="flex items-center gap-2 text-muted-foreground">
            <PhoneIcon className="size-3" />
            <Label className="whitespace-nowrap">Primary Phone</Label>
            <span className="text-foreground">
              {displayPhoneNumber(parent.primaryPhone)}
            </span>
          </div>
        </If>
        <If condition={parent.secondaryEmail}>
          <div className="flex items-center gap-2 text-muted-foreground">
            <MailIcon className="size-3" />
            <Label className="whitespace-nowrap">Secondary Email</Label>
            <span className="text-foreground">{parent.secondaryEmail}</span>
          </div>
        </If>
        <If condition={parent.secondaryPhone}>
          <div className="flex items-center gap-2 text-muted-foreground">
            <PhoneIcon className="size-3" />
            <Label className="whitespace-nowrap">Secondary Phone</Label>
            <span className="text-foreground">
              {displayPhoneNumber(parent.secondaryPhone)}
            </span>
          </div>
        </If>
      </CardContent>
    </Card>
  );
});
