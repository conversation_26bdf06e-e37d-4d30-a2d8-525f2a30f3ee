import { addStudentBaseSchema } from '@lilypad/api/schemas/students';
import type { z } from 'zod';

export const basicInfoStepSchema = addStudentBaseSchema
  .pick({
    studentIdNumber: true,
    firstName: true,
    middleName: true,
    lastName: true,
    preferredName: true,
    dateOfBirth: true,
    gender: true,
    primarySchoolId: true,
    privateSchool: true,
    outOfDistrict: true,
    grade: true,
    languageIds: true,
    primaryLanguageId: true,
  })
  .refine(
    (data) => {
      if (
        data.outOfDistrict &&
        (!data.privateSchool || data.privateSchool.trim() === '')
      ) {
        return false;
      }
      return true;
    },
    {
      message: 'Private school name is required',
      path: ['privateSchool'],
    }
  )
  .refine(
    (data) => {
      if (
        data.languageIds &&
        data.languageIds.length > 0 &&
        !data.primaryLanguageId
      ) {
        return false;
      }
      if (
        data.primaryLanguageId &&
        data.languageIds &&
        !data.languageIds.includes(data.primaryLanguageId)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        'Primary language must be selected and must be one of the selected languages',
      path: ['primaryLanguageId'],
    }
  );

export const parentsStepSchema = addStudentBaseSchema.pick({
  parents: true,
});

export const documentsStepSchema = addStudentBaseSchema.pick({
  documents: true,
});

export const caseStepSchema = addStudentBaseSchema.pick({
  caseInfo: true,
});

export const notesStepSchema = addStudentBaseSchema.pick({
  notes: true,
});

export type BasicInfoStepData = z.infer<typeof basicInfoStepSchema>;
export type ParentsStepData = z.infer<typeof parentsStepSchema>;
export type DocumentsStepData = z.infer<typeof documentsStepSchema>;
export type CaseStepData = z.infer<typeof caseStepSchema>;
export type NotesStepData = z.infer<typeof notesStepSchema>;
