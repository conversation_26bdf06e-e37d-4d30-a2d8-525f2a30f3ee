import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { useCallback } from 'react';
import { StudentFormStepId } from '../step-definitions';

interface NavigationResult {
  allowed: boolean;
  reason?: string;
  blockingIssues?: string[];
}

export function useStudentFormNavigation(formData: Partial<AddStudentSchema>) {
  const isBasicInfoValid = useCallback((data: Partial<AddStudentSchema>) => {
    return !!(
      data.studentIdNumber &&
      data.firstName &&
      data.lastName &&
      data.dateOfBirth &&
      data.grade &&
      data.gender &&
      data.primarySchoolId &&
      (data.outOfDistrict ? data.privateSchool : true) &&
      data.languageIds?.length
    );
  }, []);

  const hasMinimumParents = useCallback(
    (parents?: AddStudentSchema['parents']) => {
      return (
        (parents &&
          parents.length > 0 &&
          parents.some((parent) => parent.isPrimaryContact)) ??
        false
      );
    },
    []
  );

  const getBasicInfoErrors = useCallback((data: Partial<AddStudentSchema>) => {
    const errors: string[] = [];
    if (!data.studentIdNumber) {
      errors.push('Student ID is required');
    }
    if (!data.firstName) {
      errors.push('First name is required');
    }
    if (!data.lastName) {
      errors.push('Last name is required');
    }
    if (!data.dateOfBirth) {
      errors.push('Date of birth is required');
    }
    if (!data.grade) {
      errors.push('Grade is required');
    }
    if (!data.gender) {
      errors.push('Gender is required');
    }
    if (!data.primarySchoolId) {
      errors.push('School is required');
    }
    if (!data.languageIds?.length) {
      errors.push('At least one language is required');
    }
    return errors;
  }, []);

  const canNavigateToStep = useCallback(
    (targetStep: StudentFormStepId): NavigationResult => {
      switch (targetStep) {
        case StudentFormStepId.BASIC_INFO:
          return { allowed: true };

        case StudentFormStepId.PARENTS:
          if (!isBasicInfoValid(formData)) {
            return {
              allowed: false,
              reason: 'Complete basic information first',
              blockingIssues: getBasicInfoErrors(formData),
            };
          }
          return { allowed: true };

        case StudentFormStepId.CASE:
        case StudentFormStepId.DOCUMENTS:
          if (!isBasicInfoValid(formData)) {
            return {
              allowed: false,
              reason: 'Complete basic information first',
              blockingIssues: getBasicInfoErrors(formData),
            };
          }
          if (!hasMinimumParents(formData.parents)) {
            return {
              allowed: false,
              reason: 'Add at least one parent/guardian first',
              blockingIssues: ['At least one parent required'],
            };
          }
          return { allowed: true };

        case StudentFormStepId.REVIEW: {
          const issues: string[] = [];
          if (!isBasicInfoValid(formData)) {
            issues.push('Basic information incomplete');
          }
          if (!hasMinimumParents(formData.parents)) {
            issues.push('At least one parent required');
          }

          if (issues.length > 0) {
            return {
              allowed: false,
              reason: 'Complete required steps first',
              blockingIssues: issues,
            };
          }
          return { allowed: true };
        }

        default:
          return { allowed: false, reason: 'Unknown step' };
      }
    },
    [formData, isBasicInfoValid, hasMinimumParents, getBasicInfoErrors]
  );

  const canProceedFromStep = useCallback(
    (currentStep: StudentFormStepId): NavigationResult => {
      switch (currentStep) {
        case StudentFormStepId.BASIC_INFO:
          if (!isBasicInfoValid(formData)) {
            return {
              allowed: false,
              reason: 'Complete all required fields',
              blockingIssues: getBasicInfoErrors(formData),
            };
          }
          return { allowed: true };

        case StudentFormStepId.DOCUMENTS:
          return { allowed: true };

        case StudentFormStepId.PARENTS:
          if (!hasMinimumParents(formData.parents)) {
            return {
              allowed: false,
              reason: 'Add at least one parent/guardian',
              blockingIssues: [
                'At least one parent with primary contact required',
              ],
            };
          }
          return { allowed: true };

        case StudentFormStepId.CASE:
          return { allowed: true };

        case StudentFormStepId.REVIEW: {
          const issues: string[] = [];
          if (!isBasicInfoValid(formData)) {
            issues.push('Basic information incomplete');
          }
          if (!hasMinimumParents(formData.parents)) {
            issues.push('At least one parent required');
          }

          if (issues.length > 0) {
            return {
              allowed: false,
              reason: 'Complete required information',
              blockingIssues: issues,
            };
          }
          return { allowed: true };
        }

        default:
          return { allowed: false, reason: 'Unknown step' };
      }
    },
    [formData, isBasicInfoValid, hasMinimumParents, getBasicInfoErrors]
  );

  const isStepCompleted = useCallback(
    (stepId: StudentFormStepId): boolean => {
      switch (stepId) {
        case StudentFormStepId.BASIC_INFO:
          return isBasicInfoValid(formData);

        case StudentFormStepId.PARENTS:
          return hasMinimumParents(formData.parents);

        case StudentFormStepId.DOCUMENTS:
          return !!formData.documents?.length;

        case StudentFormStepId.CASE:
          return !!formData.caseInfo?.caseType;

        case StudentFormStepId.REVIEW:
          return (
            isBasicInfoValid(formData) && hasMinimumParents(formData.parents)
          );

        default:
          return false;
      }
    },
    [formData, isBasicInfoValid, hasMinimumParents]
  );

  return {
    canNavigateToStep,
    canProceedFromStep,
    isStepCompleted,
    isBasicInfoValid: () => isBasicInfoValid(formData),
    hasMinimumParents: () => hasMinimumParents(formData.parents),
  };
}
