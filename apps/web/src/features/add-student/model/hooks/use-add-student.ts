import { type TRPCClientError, useTRPC } from '@lilypad/api/client';
import type { AddStudentResultSchema } from '@lilypad/api/schemas/students';
import { toast } from '@lilypad/ui/components/sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

interface UseAddStudentOptions {
  onSuccess?: (result: AddStudentResultSchema) => void;
}

export function useAddStudent(options: UseAddStudentOptions = {}) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const handleSuccess = useCallback(
    (result: AddStudentResultSchema) => {
      queryClient.invalidateQueries({
        queryKey: [['students']],
        exact: false,
      });
      toast.success('Student added successfully');
      options.onSuccess?.(result);
    },
    [queryClient, options]
  );

  const { mutate: addStudent, isPending: isExecuting } = useMutation(
    trpc.students.createStudent.mutationOptions({
      onSuccess: handleSuccess,
      onError: (error: TRPCClientError) => {
        console.error('❌ FAILED TO ADD STUDENT', { error });
        toast.error('Failed to add student');
      },
    })
  );

  return {
    addStudent,
    isExecuting,
  };
}
