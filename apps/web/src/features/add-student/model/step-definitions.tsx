import {
  CheckCircleIcon,
  FilesIcon,
  FolderOpenIcon,
  ShieldUserIcon,
  UserIcon,
} from 'lucide-react';

export enum StudentFormStepId {
  BASIC_INFO = 'BASIC_INFO',
  PARENTS = 'PARENTS',
  DOCUMENTS = 'DOCUMENTS',
  CASE = 'CASE',
  REVIEW = 'REVIEW',
}

export type StudentFormStep = {
  id: StudentFormStepId;
  title: string;
  description: string;
  icon: React.ReactNode;
};

export const STUDENT_FORM_STEPS: StudentFormStep[] = [
  {
    id: StudentFormStepId.BASIC_INFO,
    title: 'Basic Information',
    description: 'Basic and contact details',
    icon: <UserIcon className="size-4" />,
  },
  {
    id: StudentFormStepId.PARENTS,
    title: 'Parents/Guardians',
    description: 'Parent and guardian information',
    icon: <ShieldUserIcon className="size-4" />,
  },
  {
    id: StudentFormStepId.CASE,
    title: 'Case Creation',
    description: 'Track evaluation progress',
    icon: <FolderOpenIcon className="size-4" />,
  },
  {
    id: StudentFormStepId.DOCUMENTS,
    title: 'Documents',
    description: 'Upload relevant documents',
    icon: <FilesIcon className="size-4" />,
  },
  {
    id: StudentFormStepId.REVIEW,
    title: 'Review & Submit',
    description: 'Review all information',
    icon: <CheckCircleIcon className="size-4" />,
  },
];

export interface StepValidationConfig {
  required: boolean;
  dependencies?: StudentFormStepId[];
  minParents?: number;
}

export const STEP_VALIDATION_CONFIG: Record<
  StudentFormStepId,
  StepValidationConfig
> = {
  [StudentFormStepId.BASIC_INFO]: { required: true },
  [StudentFormStepId.PARENTS]: { required: true, minParents: 1 },
  [StudentFormStepId.CASE]: { required: true },
  [StudentFormStepId.DOCUMENTS]: { required: false },
  [StudentFormStepId.REVIEW]: { required: false },
};
