'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { VisuallyHidden } from '@lilypad/ui/components/visually-hidden';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { CheckCircleIcon } from 'lucide-react';

const title = 'Student Added Successfully!';
const description = 'The student has been successfully added to the system. You can now add another student or go back to the students list.';

interface AddStudentSuccessDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddAnother: () => void;
  onGoBack: () => void;
}

export function AddStudentSuccessDialog({
  open,
  onOpenChange,
  onAddAnother,
  onGoBack,
}: AddStudentSuccessDialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const renderContent = (
    <div
      className={cn(
        'min-h-0 flex-1 overflow-y-auto',
        mdUp ? 'p-6' : 'p-4'
      )}
    >
      <div className="flex gap-6">
        <div className="mx-auto flex size-16 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
          <CheckCircleIcon className="size-8 text-green-600 dark:text-green-400" />
        </div>
        <div className="space-y-2">
          <h3 className="font-semibold text-lg">{title}</h3>
          <p className="text-muted-foreground text-sm leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </div>
  );

  const renderButtons = (
    <div
      className={cn(
        'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md'
      )}
    >
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="w-1/2 md:w-auto"
        onClick={onGoBack}
      >
        Go Back
      </Button>
      <Button
        type="button"
        variant="default"
        size="sm"
        className="w-1/2 md:w-auto"
        onClick={onAddAnother}
      >
        Add Another Student/Referral
      </Button>
    </div>
  );

  return (
    <>
      {mdUp ? (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent className="flex max-w-lg flex-col gap-0 p-0">
            <VisuallyHidden>
            <DialogHeader className="flex-shrink-0 px-6 py-5">
              <DialogTitle>{title}</DialogTitle>
              <DialogDescription>
                {description}
              </DialogDescription>
            </DialogHeader>
            </VisuallyHidden>
            {renderContent}
            {renderButtons}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent className="flex flex-col">
            <VisuallyHidden>
              <DrawerHeader className="flex-shrink-0 text-left">
                <DrawerTitle>{title}</DrawerTitle>
                <DrawerDescription>
                  {description}
                </DrawerDescription>
              </DrawerHeader>
            </VisuallyHidden>
            {renderContent}
            {renderButtons}
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
}
