'use client';

import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { addStudentSchema } from '@lilypad/api/schemas/students';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { routes } from '@lilypad/shared/routes';
import { Alert, AlertTitle } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import { Form } from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { useSidebar } from '@lilypad/ui/components/sidebar';
import { toast } from '@lilypad/ui/components/sonner';
import { defineStepper, useButtonLabel } from '@lilypad/ui/components/stepper';
import { cn } from '@lilypad/ui/lib/utils';
import { AlertCircleIcon, ArrowRightIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import type { SubmitHand<PERSON> } from 'react-hook-form';
import { useAddStudent } from '../model/hooks/use-add-student';
import { useStudentFormNavigation } from '../model/hooks/use-student-form-navigation';
import {
  STUDENT_FORM_STEPS,
  StudentFormStepId,
} from '../model/step-definitions';
import {
  createDefaultFormValues,
  transformFormDataToAPI,
} from '../model/transformers';
import { AddStudentSuccessDialog } from './add-student-success-dialog';
import {
  BasicInfoStep,
  CaseStep,
  DocumentsStep,
  ParentsStep,
  ReviewStep,
} from './steps';

const { Stepper: StudentStepper } = defineStepper(...STUDENT_FORM_STEPS);

export function AddStudentForm() {
  const router = useRouter();
  const [resetKey, setResetKey] = useState(0);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const { isMobile, open } = useSidebar();

  const form = useZodForm({
    schema: addStudentSchema,
    defaultValues: createDefaultFormValues(),
    mode: 'onBlur',
  });

  const navigation = useStudentFormNavigation(form.watch());

  const { addStudent, isExecuting } = useAddStudent({
    onSuccess: () => {
      setShowSuccessDialog(true);
    },
  });

  const onSubmit: SubmitHandler<AddStudentSchema> = useCallback(async (data) => {
    try {
      const apiData = await transformFormDataToAPI(data);
      addStudent(apiData);
    } catch {
      toast.error('Failed to submit form. Please try again.');
    }
  }, [addStudent]);

  const handleAddAnother = useCallback(() => {
    setShowSuccessDialog(false);
    form.reset(createDefaultFormValues());
    setResetKey(prev => prev + 1);
  }, [form]);

  const handleGoBack = useCallback(() => {
    setShowSuccessDialog(false);
    router.push(routes.app.students.Index);
  }, [router]);

  return (
    <div className="relative flex h-full flex-col">
      <Form {...form}>
        <form className="flex h-full flex-col">
          <StudentStepper.Provider key={resetKey} variant="horizontal">
            {({ methods }) => {
              // biome-ignore lint/correctness/useHookAtTopLevel: Fix later
              const buttonLabel = useButtonLabel(methods, 'Add Student');

              const handleNext = () => {
                const currentStepId = methods.current?.id;
                const canProceed = navigation.canProceedFromStep(currentStepId);

                if (canProceed.allowed) {
                  methods.next();
                }
              };

              const handleStepClick = (stepId: StudentFormStepId) => {
                const canNavigate = navigation.canNavigateToStep(stepId);

                if (canNavigate.allowed) {
                  methods.goTo(stepId);
                } else {
                  toast.error(canNavigate.reason || 'Cannot navigate to this step yet');
                }
              };

              const getCurrentStepValidation = () => {
                const currentStepId = methods.current?.id;
                return navigation.canProceedFromStep(currentStepId);
              };

              const isCurrentStepValid = () => {
                return getCurrentStepValidation().allowed;
              };

              return (
                <>
                  {/* Stepper Navigation */}
                  <div className="sticky top-0 w-full border-b">
                    <StudentStepper.Navigation className="scrollarea mx-auto max-w-7xl flex-shrink-0 overflow-x-auto bg-background px-6 py-4">
                      {methods.all.map((step) => (
                        <StudentStepper.Step
                          key={step.id}
                          of={step.id}
                          icon={step.icon}
                          onClick={() => handleStepClick(step.id)}
                        >
                          <StudentStepper.Title className="whitespace-nowrap font-medium text-sm">
                            {step.title}
                          </StudentStepper.Title>
                          <StudentStepper.Description className="whitespace-nowrap text-muted-foreground text-xs">
                            {step.description}
                          </StudentStepper.Description>
                        </StudentStepper.Step>
                      ))}
                    </StudentStepper.Navigation>
                  </div>

                  {/* Step Content */}
                  <div className="min-h-0 flex-1 overflow-hidden">
                    <StudentStepper.Panel className="scrollarea h-full space-y-4 overflow-y-auto p-4 pb-24 md:mx-auto md:max-w-3xl md:pb-28">
                      {methods.switch({
                        [StudentFormStepId.BASIC_INFO]: () => <BasicInfoStep />,
                        [StudentFormStepId.PARENTS]: () => <ParentsStep />,
                        [StudentFormStepId.DOCUMENTS]: () => <DocumentsStep />,
                        [StudentFormStepId.CASE]: () => <CaseStep />,
                        [StudentFormStepId.REVIEW]: () => (
                          <ReviewStep onEditStep={(stepId) => methods.goTo(stepId)} />
                        ),
                      })}

                      {!isCurrentStepValid() && (
                        <Alert className="text-destructive text-sm">
                          <AlertCircleIcon className="size-4" />
                          <AlertTitle>
                            {getCurrentStepValidation().reason || 'Please complete required fields'}
                          </AlertTitle>
                        </Alert>
                      )}
                    </StudentStepper.Panel>
                  </div>

                  {/* Step Footer */}
                  <div className={cn(
                    "fixed bottom-0 z-20 flex justify-center transition-all duration-200 ease-linear md:bottom-4",
                    isMobile ? "right-0 left-0" : [
                      open ? "right-0 left-[var(--sidebar-width)]" : "right-0 left-[var(--sidebar-width-icon)]"
                    ]
                  )}>
                    <div className="w-full bg-secondary md:max-w-3xl md:rounded-md md:border">
                      <StudentStepper.Controls className="flex flex-shrink-0 justify-between gap-2 p-4">
                        <If condition={methods.isFirst}>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="w-1/2 md:w-auto"
                            onClick={() => router.push(routes.app.students.Index)}
                            disabled={isExecuting}
                          >
                            Cancel
                          </Button>
                        </If>
                        <If condition={!methods.isFirst}>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="w-1/2 md:w-auto"
                            onClick={methods.prev}
                            disabled={isExecuting}
                          >
                            Previous
                          </Button>
                        </If>
                        <Button
                          size="sm"
                          className="w-1/2 md:w-auto"
                          type='button'
                          onClick={methods.isLast ? form.handleSubmit(onSubmit) : handleNext}
                          disabled={!isCurrentStepValid() || isExecuting}
                          loading={methods.isLast && isExecuting}
                        >
                          {buttonLabel}
                          {!methods.isLast && (
                            <ArrowRightIcon className="ml-2 size-4" />
                          )}
                        </Button>
                      </StudentStepper.Controls>
                    </div>
                  </div>
                </>
              );
            }}
          </StudentStepper.Provider>
        </form>
      </Form>

      <AddStudentSuccessDialog
        open={showSuccessDialog}
        onOpenChange={setShowSuccessDialog}
        onAddAnother={handleAddAnother}
        onGoBack={handleGoBack}
      />
    </div >
  );
}