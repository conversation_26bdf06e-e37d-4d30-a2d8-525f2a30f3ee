'use client';

import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import {
  CasePriorityEnum,
  CaseTypeEnum,
} from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Calendar } from '@lilypad/ui/components/calendar';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Toggle } from '@lilypad/ui/components/toggle';
import { cn } from '@lilypad/ui/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, FileTextIcon, SquareCheckIcon, SquareDashedIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

export function CaseStep() {
  const form = useFormContext<AddStudentSchema>();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Case Creation</h2>
        <p className="text-muted-foreground text-sm">
          Create a case for this student to track their evaluation progress.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="caseInfo.caseType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Case Type</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select case type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value={CaseTypeEnum.INITIAL_EVALUATION}>
                    <div className="flex items-center gap-2">
                      <FileTextIcon className="size-4 text-blue-600" />
                      Initial Evaluation
                    </div>
                  </SelectItem>
                  <SelectItem value={CaseTypeEnum.REEVALUATION}>
                    <div className="flex items-center gap-2">
                      <FileTextIcon className="size-4 text-green-600" />
                      Reevaluation
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="caseInfo.dateOfConsent"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Date of Consent</FormLabel>
              <Popover>
                <PopoverTrigger asChild className="w-full">
                  <FormControl>
                    <Button
                      className={cn(
                        'pl-3 text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                      variant="outline"
                    >
                      {field.value ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent align="start" className="w-auto p-0">
                  <Calendar
                    captionLayout="dropdown"
                    disabled={(date) => date < new Date('1900-01-01')}
                    mode="single"
                    onSelect={field.onChange}
                    selected={field.value}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="caseInfo.priority"
          render={({ field }) => (
            <FormItem className='col-span-2'>
              <FormLabel>Urgent Evaluation</FormLabel>
              <FormControl>
                <Toggle
                  className="w-full justify-start px-2 data-[state=on]:border-primary"
                  onPressedChange={(pressed) =>
                    field.onChange(pressed ? CasePriorityEnum.URGENT : CasePriorityEnum.MEDIUM)
                  }
                  pressed={field.value === CasePriorityEnum.URGENT}
                  variant="outline"
                >
                  {field.value === CasePriorityEnum.URGENT ? (
                    <SquareCheckIcon className="size-4 text-primary" />
                  ) : (
                    <SquareDashedIcon className="size-4 text-muted-foreground" />
                  )}
                  <span className="text-sm">
                    Student needs urgent evaluation
                  </span>
                </Toggle>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}