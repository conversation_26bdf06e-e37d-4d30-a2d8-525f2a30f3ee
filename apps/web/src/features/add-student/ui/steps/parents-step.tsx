'use client';

import type { AddStudentSchema, ParentSchema } from '@lilypad/api/schemas/students';
import type { ParentRelationshipEnum } from '@lilypad/db/enums';
import { normalizePhone, validatePhone } from '@lilypad/shared/phone';
import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import { toast } from '@lilypad/ui/components/sonner';
import {
  PlusIcon,
  ShieldUserIcon,
} from 'lucide-react';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { ParentFormFields } from '@/shared/ui/students/parents/parent-form-fields';
import { z } from 'zod';
import { ParentCard } from '@/features/add-students/ui/parents-dialog';

const emailSchema = z.string().email();

const EMPTY_PARENT: Partial<ParentSchema> = {
  firstName: '',
  middleName: '',
  lastName: '',
  relationshipType: undefined,
  primaryEmail: '',
  primaryPhone: '',
  secondaryEmail: '',
  secondaryPhone: '',
  isPrimaryContact: false,
  hasPickupAuthorization: false,
};

export function ParentsStep() {
  const form = useFormContext<AddStudentSchema>();
  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: 'parents',
  });

  const [showAddForm, setShowAddForm] = React.useState(false);
  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
  const [parentData, setParentData] =
    React.useState<Partial<ParentSchema>>(EMPTY_PARENT);

  const isEditing = editingIndex !== null;
  const isShowingForm = showAddForm || isEditing;

  const handleParentFieldChange = (
    field: keyof ParentSchema,
    value: string | boolean
  ) => {
    setParentData((prev) => ({ ...prev, [field]: value }));
  };

  const validateParentData = React.useCallback(() => {
    const hasFirstName = parentData.firstName?.trim();
    const hasLastName = parentData.lastName?.trim();
    const hasPrimaryEmail = parentData.primaryEmail?.trim();
    const hasSecondaryEmail = parentData.secondaryEmail?.trim();
    const hasPrimaryPhone = parentData.primaryPhone?.trim();
    const hasSecondaryPhone = parentData.secondaryPhone?.trim();

    if (!hasFirstName) { return 'First name is required'; }
    if (!hasLastName) { return 'Last name is required'; }
    if (!parentData.relationshipType) { return 'Relationship type is required'; }

    if (!(hasPrimaryEmail || hasPrimaryPhone)) {
      return 'Either primary email or primary phone is required';
    }

    if (hasPrimaryEmail && !emailSchema.safeParse(hasPrimaryEmail).success) {
      return 'Invalid primary email format';
    }

    if (hasSecondaryEmail && !emailSchema.safeParse(hasSecondaryEmail).success) {
      return 'Invalid secondary email format';
    }

    if (hasPrimaryPhone && !validatePhone(normalizePhone(hasPrimaryPhone))) {
      return 'Invalid primary phone number format';
    }

    if (hasSecondaryPhone && !validatePhone(normalizePhone(hasSecondaryPhone))) {
      return 'Invalid secondary phone number format';
    }

    return null;
  }, [parentData]);

  const handleAddParent = () => {
    const validationError = validateParentData();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    const newParent: ParentSchema = {
      firstName: parentData.firstName as string,
      middleName: parentData.middleName || undefined,
      lastName: parentData.lastName as string,
      relationshipType: parentData.relationshipType as ParentRelationshipEnum,
      primaryEmail: parentData.primaryEmail || undefined,
      primaryPhone: parentData.primaryPhone || undefined,
      secondaryEmail: parentData.secondaryEmail || undefined,
      secondaryPhone: parentData.secondaryPhone || undefined,
      isPrimaryContact:
        fields.length === 0 ? true : Boolean(parentData.isPrimaryContact),
      hasPickupAuthorization: Boolean(parentData.hasPickupAuthorization),
    };

    append(newParent);
    resetForm();
  };

  const handleUpdateParent = () => {
    if (editingIndex === null) {
      return;
    }

    const validationError = validateParentData();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    const updatedParent: ParentSchema = {
      firstName: parentData.firstName as string,
      middleName: parentData.middleName || undefined,
      lastName: parentData.lastName as string,
      relationshipType: parentData.relationshipType as ParentRelationshipEnum,
      primaryEmail: parentData.primaryEmail || undefined,
      primaryPhone: parentData.primaryPhone || undefined,
      secondaryEmail: parentData.secondaryEmail || undefined,
      secondaryPhone: parentData.secondaryPhone || undefined,
      isPrimaryContact: Boolean(parentData.isPrimaryContact),
      hasPickupAuthorization: Boolean(parentData.hasPickupAuthorization),
    };

    update(editingIndex, updatedParent);
    resetForm();
  };

  const handleEditParent = (index: number) => {
    const parent = fields[index];
    setParentData(parent);
    setEditingIndex(index);
    setShowAddForm(false);
  };

  const resetForm = () => {
    setParentData(EMPTY_PARENT);
    setShowAddForm(false);
    setEditingIndex(null);
  };

  const handleCancel = () => {
    resetForm();
  };

  const handleSubmit = () => {
    if (isEditing) {
      handleUpdateParent();
    } else {
      handleAddParent();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-lg">Parents & Guardians</h2>
        <p className="text-muted-foreground text-sm">
          Add at least one parent or guardian. One must be marked as the primary contact.
        </p>
      </div>

      <If condition={isShowingForm}>
        <Card className="gap-2 border-none p-0 shadow-none">
          <CardHeader className="p-0">
            <CardTitle className="text-base">
              {isEditing ? 'Edit Parent/Guardian' : 'Add Parent/Guardian'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-0">
            <ParentFormFields
              data={parentData}
              firstParent={fields.length === 0 && !isEditing}
              onChange={handleParentFieldChange}
            />
            <div className="flex justify-end gap-2">
              <Button
                onClick={handleCancel}
                size="sm"
                type="button"
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                disabled={Boolean(validateParentData())}
                onClick={handleSubmit}
                size="sm"
                type="button"
              >
                {isEditing ? 'Update Parent/Guardian' : 'Add Parent/Guardian'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </If>

      <If condition={!isShowingForm && fields.length > 0}>
        <div className="grid grid-cols-1 gap-4">
          {fields.map((field, index) => (
            <ParentCard
              key={field.id}
              index={index}
              parent={field}
              onEdit={handleEditParent}
              onDelete={remove} />
          ))}
          <Card
            className="cursor-pointer border-dashed text-muted-foreground shadow-none hover:bg-muted-foreground/5 hover:text-primary"
            onClick={() => setShowAddForm(true)}
          >
            <CardContent className="flex h-full items-center justify-center gap-2 p-6 text-sm transition">
              <PlusIcon className="size-4" />
              <span>Add Parent/Guardian</span>
            </CardContent>
          </Card>
        </div>
      </If>

      <If condition={fields.length === 0 && !isShowingForm}>
        <div className="rounded-lg border border-dashed p-8 text-center">
          <div className="mx-auto flex w-fit items-center justify-center rounded-lg bg-muted p-2.5">
            <ShieldUserIcon className="size-5 text-muted-foreground" />
          </div>
          <h3 className="mt-4 font-medium text-lg">
            No parents or guardians added
          </h3>
          <p className="mt-2 text-muted-foreground text-sm">
            Add at least one parent or guardian for the student. One must be
            marked as the primary contact.
          </p>
          <Button
            className="mt-4"
            onClick={() => setShowAddForm(true)}
            size="sm"
            type="button"
            variant="outline"
          >
            Add Parent/Guardian
          </Button>
        </div>
      </If>
    </div>
  );
}