'use client';

import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { If } from '@lilypad/ui/components/if';
import { formatDate } from '@lilypad/ui/lib/utils';
import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseTypeBadge } from '@/shared/ui/cases/case-type-badge';
import { StudentFormStepId } from '../../../model/step-definitions';
import { ReviewSection } from './review-section';

interface CaseInformationSectionProps {
	formData: AddStudentSchema;
	onEdit?: (stepId: StudentFormStepId) => void;
}

export function CaseInformationSection({
	formData,
	onEdit,
}: CaseInformationSectionProps) {
	return (
		<ReviewSection
			title="Case Information"
			onEdit={() => onEdit?.(StudentFormStepId.CASE)}
		>
			<div className="grid grid-cols-2 gap-4 text-sm">
				<div>
					<span className="text-muted-foreground">Case Type</span>
					<div className="mt-1">
						{formData.caseInfo?.caseType && (
							<CaseTypeBadge type={formData.caseInfo.caseType} />
						)}
					</div>
				</div>
				<div>
					<span className="text-muted-foreground">Priority</span>
					<div className="mt-1">
						{formData.caseInfo?.priority && (
							<CasePriorityBadge priority={formData.caseInfo.priority} />
						)}
					</div>
				</div>
				<If condition={formData.caseInfo?.dateOfConsent}>
					<div>
						<span className="text-muted-foreground">Date of Consent</span>
						<p className="font-medium">
							{formData.caseInfo?.dateOfConsent &&
								formatDate(formData.caseInfo.dateOfConsent)}
						</p>
					</div>
				</If>
			</div>
		</ReviewSection>
	);
}
