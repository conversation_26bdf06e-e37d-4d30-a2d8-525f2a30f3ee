'use client';

import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@lilypad/ui/components/form';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import dynamic from 'next/dynamic';
import { useFormContext } from 'react-hook-form';
import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { ReviewSection } from './review-section';

// Dynamically import the editor to avoid SSR issues
const LilypadEditor = dynamic(
	() => import('@lilypad/editor').then((mod) => mod.LilypadEditor),
	{
		ssr: false,
		loading: () => (
			<div className="space-y-2">
				<Skeleton className="h-12 w-full" />
				<Skeleton className="h-64 w-full" />
			</div>
		),
	}
);

export function NotesSection() {
	const form = useFormContext<AddStudentSchema>();

	return (
		<ReviewSection title="Notes" showEditButton={false}>
			<FormField
				control={form.control}
				name="notes.content"
				render={({ field }) => (
					<FormItem>
						<FormLabel className="sr-only">Notes</FormLabel>
						<FormControl>
							<LilypadEditor
								className="min-h-[200px]"
								editorContentClassName="p-4"
								onChange={field.onChange}
								placeholder="Enter any additional notes about the student..."
								value={field.value}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</ReviewSection>
	);
}
