'use client';

import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { If } from '@lilypad/ui/components/if';
import { FileIcon } from 'lucide-react';
import { DocumentCategoryBadge } from '@/shared/ui/documents/document-category-badge';
import { StudentFormStepId } from '../../../model/step-definitions';
import { ReviewSection } from './review-section';

interface DocumentsSectionProps {
	formData: AddStudentSchema;
	onEdit?: (stepId: StudentFormStepId) => void;
}

export function DocumentsSection({ formData, onEdit }: DocumentsSectionProps) {
	return (
		<ReviewSection
			title="Documents"
			onEdit={() => onEdit?.(StudentFormStepId.DOCUMENTS)}
		>
			<If
				condition={formData.documents?.length}
				fallback={
					<p className="text-muted-foreground text-sm">No documents uploaded</p>
				}
			>
				<div className="space-y-2">
					{formData.documents?.map((doc, index) => {
						const fileName =
							doc.file instanceof File ? doc.file.name : doc.file.name;
						return (
							<div
								key={`document-${fileName}-${index}`}
								className="flex items-center gap-3 text-sm"
							>
								<FileIcon className="size-4 text-muted-foreground" />
								<span className="flex-1">{fileName}</span>
								{doc.category && (
									<DocumentCategoryBadge category={doc.category} />
								)}
							</div>
						);
					})}
				</div>
			</If>
		</ReviewSection>
	);
}
