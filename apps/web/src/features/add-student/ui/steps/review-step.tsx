'use client';

import type { AddStudentSchema } from '@lilypad/api/schemas/students';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@lilypad/ui/components/card';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { formatDate } from '@lilypad/ui/lib/utils';
import { CalendarIcon, EditIcon, FileIcon, MailIcon, PhoneIcon, SchoolIcon, UserIcon } from 'lucide-react';
import dynamic from 'next/dynamic';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { calculateAge } from '@/shared/lib/utils';
import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseTypeBadge } from '@/shared/ui/cases/case-type-badge';
import { DocumentCategoryBadge } from '@/shared/ui/documents/document-category-badge';
import { ParentRelationshipBadge } from '@/shared/ui/students/parents/parent-relationship-badge';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';
import { StudentFormStepId } from '../../model/step-definitions';

interface ReviewStepProps {
	onEditStep?: (stepId: StudentFormStepId) => void;
}

export function ReviewStep({ onEditStep }: ReviewStepProps) {
	const form = useFormContext<AddStudentSchema>();
	const formData = form.watch();

	return (
		<div className="space-y-6">
			<div>
				<h2 className="font-semibold text-lg">Review & Submit</h2>
				<p className="text-muted-foreground text-sm">
					Review all the information below and make any necessary changes before
					submitting.
				</p>
			</div>

			<BasicInformationSection formData={formData} onEdit={onEditStep} />
			<ParentsGuardiansSection formData={formData} onEdit={onEditStep} />
			<DocumentsSection formData={formData} onEdit={onEditStep} />
			<CaseInformationSection formData={formData} onEdit={onEditStep} />
			<NotesSection />
		</div>
	);
}

function ReviewSection(props: React.PropsWithChildren<{ title: string; onEdit?: VoidFunction }>) {
	return (
		<div className="rounded-xl border border-border bg-muted/80 dark:bg-muted/20">
			<Card className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none">
				<CardHeader>
					<CardTitle className="text-base">{props.title}</CardTitle>
				</CardHeader>
				<CardContent>{props.children}</CardContent>
			</Card>
			<If condition={props.onEdit}>
				{(onEdit) => (
					<div className="flex items-center justify-end space-x-2 p-3">
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={onEdit}
							className="flex items-center gap-1"
						>
							<EditIcon className="size-3" />
							Edit
						</Button>
					</div>
				)}
			</If>
		</div>
	);
}

interface ReviewIndividualSectionProps {
	formData: AddStudentSchema;
	onEdit?: (stepId: StudentFormStepId) => void;
}

function BasicInformationSection({ formData, onEdit }: ReviewIndividualSectionProps) {
	const age = React.useMemo(() => {
		if (!formData.dateOfBirth) {
			return 0;
		}
		return calculateAge(formData.dateOfBirth.toISOString());
	}, [formData.dateOfBirth]);

	return (
		<ReviewSection
			title="Basic Information"
			onEdit={() => onEdit?.(StudentFormStepId.BASIC_INFO)}
		>
			<div className="grid grid-cols-2 gap-4 text-sm">
				<div>
					<span className="text-muted-foreground">Full Name</span>
					<p className="font-medium">
						{formData.firstName} {formData.middleName} {formData.lastName}
					</p>
				</div>
				<If condition={formData.preferredName}>
					<div>
						<span className="text-muted-foreground">Preferred Name</span>
						<p className="font-medium">{formData.preferredName}</p>
					</div>
				</If>
				<div>
					<span className="text-muted-foreground">Student ID</span>
					<p className="font-medium">{formData.studentIdNumber}</p>
				</div>
				<div>
					<span className="text-muted-foreground">Gender</span>
					<div className="mt-1">
						{formData.gender && <StudentGenderBadge gender={formData.gender} />}
					</div>
				</div>
				<div>
					<span className="text-muted-foreground">Date of Birth</span>
					<div className="flex items-center gap-2">
						<CalendarIcon className="size-3 text-muted-foreground" />
						<p className="font-medium">
							{formData.dateOfBirth && formatDate(formData.dateOfBirth)} ({age}{' '}
							years old)
						</p>
					</div>
				</div>
				<If condition={formData.primarySchoolId}>
					<div>
						<span className="text-muted-foreground">Primary School</span>
						<div className="flex items-center gap-2">
							<SchoolIcon className="size-3 text-muted-foreground" />
							<p className="font-medium">School Selected</p>
						</div>
					</div>
				</If>
				<If condition={formData.grade}>
					<div>
						<span className="text-muted-foreground">Grade</span>
						<div className="mt-1">
							<SchoolGradeBadge grade={formData.grade} />
						</div>
					</div>
				</If>
				<If condition={formData.languageIds?.length}>
					<div className="col-span-2">
						<span className="text-muted-foreground">Languages</span>
						<p className="font-medium">
							{formData.languageIds?.length} language
							{formData.languageIds?.length === 1 ? '' : 's'} selected
						</p>
					</div>
				</If>
			</div>
		</ReviewSection>
	);
}



function ParentsGuardiansSection({ formData, onEdit }: ReviewIndividualSectionProps) {
	return (
		<ReviewSection
			title="Parents & Guardians"
			onEdit={() => onEdit?.(StudentFormStepId.PARENTS)}
		>
			<If condition={formData.parents?.length}>
				<div className="space-y-3">
					{formData.parents?.map((parent, index) => (
						<div
							key={`parent-${parent.firstName}-${parent.lastName}-${index}`}
							className="flex items-start gap-3 rounded-lg border p-3"
						>
							<UserIcon className="mt-0.5 size-4 text-muted-foreground" />
							<div className="flex-1 space-y-2">
								<div className="flex items-center gap-2">
									<p className="font-medium">
										{parent.firstName} {parent.middleName} {parent.lastName}
									</p>
									<ParentRelationshipBadge
										relationship={parent.relationshipType}
									/>
									<If condition={parent.isPrimaryContact}>
										<Badge
											className="border-primary bg-primary/10 text-primary"
											variant="outline"
										>
											Primary Contact
										</Badge>
									</If>
								</div>
								<div className="grid grid-cols-2 gap-2 text-sm">
									<If condition={parent.primaryEmail}>
										<div className="flex items-center gap-2 text-muted-foreground">
											<MailIcon className="size-3" />
											<span>{parent.primaryEmail}</span>
										</div>
									</If>
									<If condition={parent.primaryPhone}>
										<div className="flex items-center gap-2 text-muted-foreground">
											<PhoneIcon className="size-3" />
											<span>{parent.primaryPhone}</span>
										</div>
									</If>
								</div>
							</div>
						</div>
					))}
				</div>
			</If>
		</ReviewSection>
	);
}

function CaseInformationSection({ formData, onEdit }: ReviewIndividualSectionProps) {
	return (
		<ReviewSection
			title="Case Information"
			onEdit={() => onEdit?.(StudentFormStepId.CASE)}
		>
			<div className="grid grid-cols-2 gap-4 text-sm">
				<div>
					<span className="text-muted-foreground">Case Type</span>
					<div className="mt-1">
						{formData.caseInfo?.caseType && (
							<CaseTypeBadge type={formData.caseInfo.caseType} />
						)}
					</div>
				</div>
				<div>
					<span className="text-muted-foreground">Priority</span>
					<div className="mt-1">
						{formData.caseInfo?.priority && (
							<CasePriorityBadge priority={formData.caseInfo.priority} />
						)}
					</div>
				</div>
				<If condition={formData.caseInfo?.dateOfConsent}>
					<div>
						<span className="text-muted-foreground">Date of Consent</span>
						<p className="font-medium">
							{formData.caseInfo?.dateOfConsent &&
								formatDate(formData.caseInfo.dateOfConsent)}
						</p>
					</div>
				</If>
			</div>
		</ReviewSection>
	);
}


function DocumentsSection({ formData, onEdit }: ReviewIndividualSectionProps) {
	return (
		<ReviewSection
			title="Documents"
			onEdit={() => onEdit?.(StudentFormStepId.DOCUMENTS)}
		>
			<If
				condition={formData.documents?.length}
				fallback={
					<p className="text-muted-foreground text-sm">No documents uploaded</p>
				}
			>
				<div className="space-y-2">
					{formData.documents?.map((doc, index) => {
						const fileName =
							doc.file instanceof File ? doc.file.name : doc.file.name;
						// Use a combination of fileName and file size for a more unique key
						const fileKey = doc.file instanceof File
							? `${fileName}-${doc.file.size}-${doc.file.lastModified}`
							: `${fileName}-${index}`;
						return (
							<div
								key={fileKey}
								className="flex items-center gap-3 text-sm"
							>
								<FileIcon className="size-4 text-muted-foreground" />
								<span className="flex-1">{fileName}</span>
								{doc.category && (
									<DocumentCategoryBadge category={doc.category} />
								)}
							</div>
						);
					})}
				</div>
			</If>
		</ReviewSection>
	);
}


const LilypadEditor = dynamic(
	() => import('@lilypad/editor').then((mod) => mod.LilypadEditor),
	{
		ssr: false,
		loading: () => (
			<div className="space-y-2">
				<Skeleton className="h-12 w-full" />
				<Skeleton className="h-64 w-full" />
			</div>
		),
	}
);

export function NotesSection() {
	const form = useFormContext<AddStudentSchema>();
	return (
		<ReviewSection title="Notes">
			<FormField
				control={form.control}
				name="notes.content"
				render={({ field }) => (
					<FormItem>
						<FormLabel className="sr-only">Notes</FormLabel>
						<FormControl>
							<LilypadEditor
								className="min-h-[200px]"
								editorContentClassName="p-4"
								onChange={field.onChange}
								placeholder="Enter any additional notes about the student..."
								value={field.value}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</ReviewSection>
	);
}
