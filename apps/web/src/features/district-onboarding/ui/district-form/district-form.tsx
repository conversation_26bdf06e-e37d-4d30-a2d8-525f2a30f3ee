'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@lilypad/ui/components/button';
import { Form } from '@lilypad/ui/components/form';
import { defineStepper, useButtonLabel } from '@lilypad/ui/components/stepper';
import {
  ArrowRightIcon,
  ClockIcon,
  InfoIcon,
  MapPinIcon,
  SettingsIcon,
} from 'lucide-react';
import { type FieldValues, useForm } from 'react-hook-form';
import { selectFormData, useDistrictConfigurationStore } from '../../model';
import {
  type CreateDistrictForm,
  createDistrictSchema,
} from '../../model/schemas';
import { DistrictAddressForm } from './district-address-form';
import { DistrictAvailabilitiesForm } from './district-availabilities-form';
import { DistrictGeneralInfoForm } from './district-general-info-form';
import { DistrictPreferencesForm } from './district-preferences-form';

// biome-ignore lint/style/noEnum: Not needed
enum DistrictStepId {
  GENERAL_INFO = 'general-info',
  ADDRESS = 'address',
  AVAILABILITIES = 'availabilities',
  PREFERENCES = 'preferences',
}

const DISTRICT_STEPS = [
  {
    id: DistrictStepId.GENERAL_INFO,
    title: 'General Info',
    description: 'Basic district information',
    icon: <InfoIcon className="size-4" />,
  },
  {
    id: DistrictStepId.ADDRESS,
    title: 'Address',
    description: 'District address details',
    icon: <MapPinIcon className="size-4" />,
  },
  {
    id: DistrictStepId.AVAILABILITIES,
    title: 'Availability',
    description: 'Operating hours and availability',
    icon: <ClockIcon className="size-4" />,
  },
  {
    id: DistrictStepId.PREFERENCES,
    title: 'Preferences',
    description: 'District configuration',
    icon: <SettingsIcon className="size-4" />,
  },
];

const { Stepper: DistrictStepper } = defineStepper(...DISTRICT_STEPS);

interface DistrictFormProps {
  onComplete?: (data: CreateDistrictForm) => void;
  onNext?: () => void;
}

export function DistrictForm({ onComplete, onNext }: DistrictFormProps) {
  const formData = useDistrictConfigurationStore(selectFormData);
  const updateDistrictForm = useDistrictConfigurationStore(
    (state) => state.updateDistrictForm
  );
  const updateDistrict = useDistrictConfigurationStore(
    (state) => state.updateDistrict
  );

  const form = useForm({
    resolver: zodResolver(createDistrictSchema),
    values: formData.district, // Controlled by store
    mode: 'onBlur',
  });

  const validateCurrentStep = async (
    stepId: DistrictStepId
  ): Promise<boolean> => {
    const fieldsToValidate: (keyof CreateDistrictForm)[] = [];

    switch (stepId) {
      case DistrictStepId.GENERAL_INFO:
        fieldsToValidate.push('generalInfo');
        break;
      case DistrictStepId.ADDRESS:
        fieldsToValidate.push('address');
        break;
      case DistrictStepId.AVAILABILITIES:
        fieldsToValidate.push('availabilities');
        break;
      case DistrictStepId.PREFERENCES:
        fieldsToValidate.push('preferences');
        break;
      default:
        return false;
    }

    const isValid = await form.trigger(fieldsToValidate);

    // Only sync to store if validation passes
    if (isValid) {
      const currentFormData = form.getValues() as CreateDistrictForm;
      updateDistrictForm(currentFormData);
    }

    return isValid;
  };

  const handleDistrictComplete = (values: CreateDistrictForm | FieldValues) => {
    const districtData = values as CreateDistrictForm;
    console.log('District completed:', districtData);

    // Update form data in store first
    updateDistrictForm(districtData);

    // Update district completion state
    updateDistrict({
      generalInfo: districtData.generalInfo,
      address: districtData.address,
      availabilities: districtData.availabilities,
      preferences: districtData.preferences,
    });

    onComplete?.(districtData);
    onNext?.();
  };

  return (
    <Form {...form}>
      <DistrictStepper.Provider className="h-full" variant="horizontal">
        {({ methods }) => {
          // biome-ignore lint/correctness/useHookAtTopLevel: Fix later
          const buttonLabel = useButtonLabel(methods, 'Complete District');

          return (
            <div className="flex h-full flex-col">
              {/* Navigation */}

              <div className="mb-4">
                <h3 className="font-semibold">District Information</h3>
                <p className="text-muted-foreground text-sm">
                  Configure the district's basic information, address,
                  availabilities, and preferences.
                </p>
              </div>

              <DistrictStepper.Navigation className="border-y py-4">
                {methods.all.map((step: (typeof DISTRICT_STEPS)[number]) => (
                  <DistrictStepper.Step
                    icon={step.icon}
                    key={step.id}
                    of={step.id}
                    onClick={() => methods.goTo(step.id)}
                  >
                    <DistrictStepper.Title className="font-medium text-sm">
                      {step.title}
                    </DistrictStepper.Title>
                    {/* <DistrictStepper.Description className="text-xs text-muted-foreground">
											{step.description}
										</DistrictStepper.Description> */}
                  </DistrictStepper.Step>
                ))}
              </DistrictStepper.Navigation>

              {/* Content */}
              <div className="flex-1 overflow-auto py-4">
                {methods.switch({
                  [DistrictStepId.GENERAL_INFO]: () => (
                    <DistrictStepper.Panel>
                      <DistrictGeneralInfoForm />
                    </DistrictStepper.Panel>
                  ),
                  [DistrictStepId.ADDRESS]: () => (
                    <DistrictStepper.Panel>
                      <DistrictAddressForm />
                    </DistrictStepper.Panel>
                  ),
                  [DistrictStepId.AVAILABILITIES]: () => (
                    <DistrictStepper.Panel>
                      <DistrictAvailabilitiesForm />
                    </DistrictStepper.Panel>
                  ),
                  [DistrictStepId.PREFERENCES]: () => (
                    <DistrictStepper.Panel>
                      <DistrictPreferencesForm />
                    </DistrictStepper.Panel>
                  ),
                })}
              </div>

              {/* Controls */}
              <div className="pt-4">
                <DistrictStepper.Controls>
                  <Button
                    disabled={methods.isFirst}
                    onClick={methods.prev}
                    size="sm"
                    type="button"
                    variant="outline"
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={async () => {
                      const isValid = await validateCurrentStep(
                        methods.current.id as DistrictStepId
                      );
                      if (isValid) {
                        if (methods.isLast) {
                          form.handleSubmit(handleDistrictComplete)();
                        } else {
                          methods.next();
                        }
                      }
                    }}
                    size="sm"
                    type="button"
                  >
                    {buttonLabel}
                    {!methods.isLast && <ArrowRightIcon className="size-4" />}
                  </Button>
                </DistrictStepper.Controls>
              </div>
            </div>
          );
        }}
      </DistrictStepper.Provider>
    </Form>
  );
}
