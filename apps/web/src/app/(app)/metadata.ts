import { createMetadataTitle } from '@lilypad/shared';
import type { Metadata } from 'next';

export const CASES_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Cases'),
};

export const DASHBOARD_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Dashboard'),
};

export const STUDENTS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Students'),
};

export const ADD_STUDENT_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Add Student / Referral'),
};

export const ADD_MULTIPLE_STUDENTS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Add Students / Referrals'),
};

export const CALENDAR_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Calendar'),
};

export const TASKS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Tasks'),
};

export const DISTRICTS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Districts'),
};
