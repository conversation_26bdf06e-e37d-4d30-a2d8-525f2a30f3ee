import {
  type DocumentCategoryEnum,
  DocumentCategoryEnumMap,
} from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const documentCategoryBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      category: {
        ASSESSMENT:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        BACKGROUND:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        CONSENT_FORM:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        OTHER:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

interface DocumentCategoryBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof documentCategoryBadgeVariants> {
  category: DocumentCategoryEnum;
}

export function DocumentCategoryBadge({
  className,
  category,
  ...props
}: DocumentCategoryBadgeProps) {
  if (!category) {
    return null;
  }

  return (
    <div
      className={cn(documentCategoryBadgeVariants({ category }), className)}
      {...props}
    >
      {DocumentCategoryEnumMap[category]}
    </div>
  );
}

export { documentCategoryBadgeVariants };
