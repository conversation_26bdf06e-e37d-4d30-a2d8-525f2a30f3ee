'use client';

import { useTR<PERSON> } from '@lilypad/api/client';
import { Button } from '@lilypad/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@lilypad/ui/components/command';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { cn } from '@lilypad/ui/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { CheckIcon, ChevronsUpDownIcon, SchoolIcon } from 'lucide-react';
import { useMemo, useState } from 'react'
import { useFormContext } from 'react-hook-form';

interface SchoolSelectProps {
  fieldName: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
}

export function SchoolSelect({
  fieldName,
  label = 'School',
  placeholder = 'Select a school',
  required = false
}: SchoolSelectProps) {
  const form = useFormContext();
  const trpc = useTRPC();
  const [open, setOpen] = useState(false);
  const { data: schools, isError: hasError } = useQuery(trpc.schools.getSchools.queryOptions());

  const selectedSchool = useMemo(
    () => schools?.find((school) => school.id === form.getValues(fieldName)),
    [form, fieldName, schools]
  );

  const handleSelect = (schoolId: string) => {
    form.setValue(fieldName, schoolId);
    setOpen(false);
  };


  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label} {!required && <OptionalBadge />}</FormLabel>
          <Popover modal={true} onOpenChange={setOpen} open={open}>
            <FormControl>
              <PopoverTrigger asChild>
                <Button
                  aria-expanded={open}
                  aria-label="Select school"
                  className={cn(
                    'w-full justify-between',
                    hasError && 'border-destructive'
                  )}
                  role="combobox"
                  variant="outline"
                >
                  {selectedSchool ? (
                    <span className="truncate text-sm">{selectedSchool.name}</span>
                  ) : (
                    <span className="font-normal text-muted-foreground">
                      {placeholder}
                    </span>
                  )}
                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
            </FormControl>
            <PopoverContent align="start" className="w-80 p-0">
              <Command>
                <CommandInput placeholder="Search schools..." />
                <CommandList>
                  <CommandEmpty>No school found.</CommandEmpty>
                  <CommandGroup>
                    {schools?.map((school) => (
                      <CommandItem
                        key={school.id}
                        onSelect={() => handleSelect(school.id)}
                        value={school.name}
                      >
                        <CheckIcon
                          className={cn(
                            'mr-2 h-4 w-4',
                            field.value === school.id ? 'opacity-100' : 'opacity-0'
                          )}
                        />
                        <SchoolIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span className="truncate">{school.name}</span>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
