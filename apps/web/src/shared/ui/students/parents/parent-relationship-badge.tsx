import {
  type ParentRelationshipEnum,
  ParentRelationshipEnumMap,
} from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const parentRelationshipBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      relationship: {
        MOTHER:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        FATHER:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        
        GUARDIAN:
          'border-amber-200 bg-amber-100 text-amber-800 dark:border-amber-700/50 dark:bg-amber-800/20 dark:text-amber-300',
        
        STEP_MOTHER:
          'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        STEP_FATHER:
          'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        
        ADOPTIVE_MOTHER:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        ADOPTIVE_FATHER:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        
        FOSTER_MOTHER:
          'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        FOSTER_FATHER:
          'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        
        GRANDMOTHER:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        GRANDFATHER:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        
        AUNT:
          'border-teal-200 bg-teal-100 text-teal-800 dark:border-teal-700/50 dark:bg-teal-800/20 dark:text-teal-300',
        UNCLE:
          'border-teal-200 bg-teal-100 text-teal-800 dark:border-teal-700/50 dark:bg-teal-800/20 dark:text-teal-300',
        OTHER_RELATIVE:
          'border-teal-200 bg-teal-100 text-teal-800 dark:border-teal-700/50 dark:bg-teal-800/20 dark:text-teal-300',

        NON_RELATIVE:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
        UNKNOWN:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

interface ParentRelationshipBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof parentRelationshipBadgeVariants> {
  relationship: ParentRelationshipEnum;
}

export function ParentRelationshipBadge({
  className,
  relationship,
  ...props
}: ParentRelationshipBadgeProps) {
  if (!relationship) {
    return null;
  }

  return (
    <div
      className={cn(parentRelationshipBadgeVariants({ relationship }), className)}
      {...props}
    >
      {ParentRelationshipEnumMap[relationship]}
    </div>
  );
}

export { parentRelationshipBadgeVariants };
