'use client';

import type { ParentSchema } from '@lilypad/api/schemas/students';
import { ParentRelationshipEnumMap } from '@lilypad/db/enums';
import { formatUSPhone } from "@lilypad/shared/phone";
import { Input } from '@lilypad/ui/components/input';
import { Label } from '@lilypad/ui/components/label';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Toggle } from '@lilypad/ui/components/toggle';
import { SquareCheckIcon, SquareDashedIcon } from 'lucide-react';

interface ParentFormFieldsProps {
  data: Partial<ParentSchema>;
  onChange: (field: keyof ParentSchema, value: string | boolean) => void;
  firstParent?: boolean;
}

export function ParentFormFields({
  data,
  onChange,
  firstParent = false,
}: ParentFormFieldsProps) {
  return (
    <div className="grid grid-cols-2 gap-3">
      <div className="space-y-1">
        <Label className="font-medium text-sm">First Name</Label>
        <Input
          onChange={(e) => onChange('firstName', e.target.value)}
          placeholder="John"
          value={data.firstName || ''}
        />
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">Last Name</Label>
        <Input
          onChange={(e) => onChange('lastName', e.target.value)}
          placeholder="Doe"
          value={data.lastName || ''}
        />
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">
          Middle Name <OptionalBadge />
        </Label>
        <Input
          onChange={(e) => onChange('middleName', e.target.value)}
          placeholder="Michael"
          value={data.middleName || ''}
        />
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">Relationship</Label>
        <Select
          onValueChange={(value) => onChange('relationshipType', value)}
          value={data.relationshipType}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select relationship" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(ParentRelationshipEnumMap).map(([key, label]) => (
              <SelectItem key={key} value={key}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">Primary Email</Label>
        <Input
          onChange={(e) => onChange('primaryEmail', e.target.value)}
          placeholder="<EMAIL>"
          type="email"
          value={data.primaryEmail || ''}
        />
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">Secondary Email</Label>
        <Input
          onChange={(e) => onChange('secondaryEmail', e.target.value)}
          placeholder="<EMAIL>"
          type="email"
          value={data.secondaryEmail || ''}
        />
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">Primary Phone</Label>
        <Input
          onChange={(e) => onChange('primaryPhone', formatUSPhone(e.target.value))}
          placeholder="(*************"
          type="tel"
          value={data.primaryPhone || ''}
        />
      </div>

      <div className="space-y-1">
        <Label className="font-medium text-sm">Secondary Phone</Label>
        <Input
          onChange={(e) => onChange('secondaryPhone', formatUSPhone(e.target.value))}
          placeholder="(*************"
          type="tel"
          value={data.secondaryPhone || ''}
        />
      </div>

      <div className="flex flex-row items-start">
        <Toggle
          className="w-full justify-start px-2 data-[state=on]:border-primary"
          disabled={firstParent}
          onPressedChange={(pressed) => onChange('isPrimaryContact', pressed)}
          pressed={data.isPrimaryContact}
          variant="outline"
        >
          {data.isPrimaryContact ? (
            <SquareCheckIcon className="size-4 text-primary" />
          ) : (
            <SquareDashedIcon className="size-4 text-muted-foreground" />
          )}
          <span className="text-xs">Is Primary Contact</span>
        </Toggle>
      </div>

      <div className="flex flex-row items-start">
        <Toggle
          className="w-full justify-start px-2 data-[state=on]:border-primary"
          onPressedChange={(pressed) =>
            onChange('hasPickupAuthorization', pressed)
          }
          pressed={data.hasPickupAuthorization}
          variant="outline"
        >
          {data.hasPickupAuthorization ? (
            <SquareCheckIcon className="size-4 text-primary" />
          ) : (
            <SquareDashedIcon className="size-4 text-muted-foreground" />
          )}
          <span className="text-xs">Has Pickup Authorization</span>
        </Toggle>
      </div>
    </div>
  );
}
