'use client';

import type {
  CaseP<PERSON>rityEnumMap,
  CaseStatusEnumMap,
  CaseTypeEnumMap,
} from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { DataTableColumnHeader } from '@lilypad/ui/data-table/data-table-column-header';
import { cn } from '@lilypad/ui/lib/utils';
import type { ColumnDef } from '@tanstack/react-table';
import { Building2, Calendar } from 'lucide-react';
import type { CaseTableRow } from '@/entities/cases/model/schema';
import {
  casePriorityOptions,
  caseStatusOptions,
  caseTypeOptions,
  isActiveOptions,
} from '@/entities/cases/model/schema';
import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseStatusBadge } from '@/shared/ui/cases/case-status-badge';
import { CaseTypeBadge } from '@/shared/ui/cases/case-type-badge';
import { UserAvatarStack } from '@/shared/ui/users/user-avatar-stack';

export const casesTableColumns: ColumnDef<CaseTableRow>[] = [
  {
    accessorKey: 'studentFullName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Student" />
    ),
    cell: ({ row }) => {
      const caseItem = row.original;
      return (
        <div className="flex flex-col">
          <span className="font-medium">{caseItem.studentFullName}</span>
          <span className="text-muted-foreground text-sm">
            Case #{caseItem.displayId}
          </span>
        </div>
      );
    },
    meta: {
      label: 'Student Name',
      variant: 'text',
      placeholder: 'Search students...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as keyof typeof CaseStatusEnumMap;
      return <CaseStatusBadge status={status} />;
    },
    meta: {
      label: 'Status',
      variant: 'select',
      options: caseStatusOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'priority',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Priority" />
    ),
    cell: ({ row }) => {
      const priority = row.getValue(
        'priority'
      ) as keyof typeof CasePriorityEnumMap;
      return <CasePriorityBadge priority={priority} />;
    },
    meta: {
      label: 'Priority',
      variant: 'select',
      options: casePriorityOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'caseType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Type" />
    ),
    cell: ({ row }) => {
      const caseType = row.getValue('caseType') as keyof typeof CaseTypeEnumMap;
      return <CaseTypeBadge type={caseType} />;
    },
    meta: {
      label: 'Case Type',
      variant: 'select',
      options: caseTypeOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'schoolName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="School" />
    ),
    cell: ({ row }) => {
      const caseItem = row.original;
      return (
        <div className="flex items-start gap-2">
          <Building2 className="mt-1 size-3 text-muted-foreground" />
          <div className="flex flex-col">
            <span className="font-medium text-sm">
              {caseItem.schoolName || 'No School'}
            </span>
            <span className="text-muted-foreground text-xs">
              {caseItem.districtName || 'No District'}
            </span>
          </div>
        </div>
      );
    },
    meta: {
      label: 'School',
      variant: 'text',
      placeholder: 'Search schools...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'assignedUserCount',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Assigned" />
    ),
    cell: ({ row }) => {
      const caseItem = row.original;
      return <UserAvatarStack userNames={caseItem.assignedUserNames} />;
    },
    enableSorting: true,
  },
  {
    accessorKey: 'evaluationDueDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Due Date" />
    ),
    cell: ({ row }) => {
      const dueDate = row.getValue('evaluationDueDate') as Date | null;
      if (!dueDate) {
        return (
          <span className="text-muted-foreground text-sm">No due date</span>
        );
      }

      const isOverdue = new Date(dueDate) < new Date();
      return (
        <div className="flex items-center gap-2">
          <Calendar className="size-3 text-muted-foreground" />
          <span
            className={cn('text-sm', isOverdue && 'font-medium text-red-600')}
          >
            {new Date(dueDate).toLocaleDateString()}
          </span>
          {isOverdue && (
            <Badge className="text-xs" variant="destructive">
              Overdue
            </Badge>
          )}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'isActive',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Active" />
    ),
    cell: ({ row }) => {
      const isActive = row.getValue('isActive') as boolean;
      return (
        <Badge
          className={
            isActive ? 'border-primary bg-primary/10 text-primary' : ''
          }
          variant={isActive ? 'default' : 'outline'}
        >
          {isActive ? 'Yes' : 'No'}
        </Badge>
      );
    },
    meta: {
      label: 'Active Status',
      variant: 'select',
      options: isActiveOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      const isActive = row.getValue(id) as boolean;
      return value.includes(isActive.toString());
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue('createdAt') as Date;
      return (
        <div className="flex items-center gap-2">
          <Calendar className="size-3 text-muted-foreground" />
          <span className="text-sm">
            {new Date(createdAt).toLocaleDateString()}
          </span>
        </div>
      );
    },
    enableSorting: true,
  },
];
