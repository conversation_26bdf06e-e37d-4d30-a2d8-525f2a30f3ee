'use client';

import { baseUrl, getPathname, routes } from '@lilypad/shared/routes';

import { Kbd, Kbd<PERSON><PERSON> } from '@lilypad/ui/components/kbd';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@lilypad/ui/components/sidebar';
import { AnimatedLandmarkIcon } from '@lilypad/ui/icons';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useHotkeys } from 'react-hotkeys-hook';

const MAIN_NAV_SHORTCUTS = {
  districts: 'i',
};

const MAIN_NAV_ITEMS = [
  {
    title: 'Districts',
    url: routes.app.districts.Index,
    icon: <AnimatedLandmarkIcon />,
    shortcut: (
      <Kbd size="sm" variant="ghost">
        <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
        <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
        <KbdKey>{MAIN_NAV_SHORTCUTS.districts.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
];

export function NavSuperUser() {
  const router = useRouter();
  const pathname = usePathname();

  useHotkeys(`mod+shift+${MAIN_NAV_SHORTCUTS.districts}`, (event) => {
    event.preventDefault();
    router.push(routes.app.districts.Index);
  });

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Admin</SidebarGroupLabel>
      <SidebarMenu className="text-muted-foreground">
        {MAIN_NAV_ITEMS.map((item) => {
          const isActive = pathname === getPathname(item.url, baseUrl.App);

          return (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url}>
                <SidebarMenuButton
                  className="group/menu-button cursor-pointer"
                  isActive={isActive}
                  tooltip={item.title}
                >
                  {item.icon}
                  <span>{item.title}</span>
                  <SidebarMenuBadge className="hidden group-hover/menu-button:block">
                    {item.shortcut}
                  </SidebarMenuBadge>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
