'use client';

import { baseUrl, getPathname, routes } from '@lilypad/shared/routes';
import { Kbd, KbdKey } from '@lilypad/ui/components/kbd';
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@lilypad/ui/components/sidebar';
import {
  AnimatedCalendarDaysIcon,
  AnimatedFoldersIcon,
  AnimatedHomeIcon,
  AnimatedUsersIcon,
} from '@lilypad/ui/icons';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useHotkeys } from 'react-hotkeys-hook';

const MAIN_NAV_SHORTCUTS = {
  dashboard: 'h',
  students: 'u',
  calendar: 'l',
  cases: 'c',
};

const MAIN_NAV_ITEMS = [
  {
    title: 'Dashboard',
    url: routes.app.dashboard.Index,
    icon: <AnimatedHomeIcon />,
    shortcut: (
      <Kbd size="sm" variant="ghost">
        <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
        <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
        <KbdKey>{MAIN_NAV_SHORTCUTS.dashboard.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Cases',
    url: routes.app.cases.Index,
    icon: <AnimatedFoldersIcon />,
    shortcut: (
      <Kbd size="sm" variant="ghost">
        <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
        <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
        <KbdKey>{MAIN_NAV_SHORTCUTS.cases.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Students',
    url: routes.app.students.Index,
    icon: <AnimatedUsersIcon />,
    shortcut: (
      <Kbd size="sm" variant="ghost">
        <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
        <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
        <KbdKey>{MAIN_NAV_SHORTCUTS.students.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Calendar',
    url: routes.app.calendar.Index,
    icon: <AnimatedCalendarDaysIcon />,
    shortcut: (
      <Kbd size="sm" variant="ghost">
        <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
        <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
        <KbdKey>{MAIN_NAV_SHORTCUTS.calendar.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
];

export function NavMain() {
  const router = useRouter();
  const pathname = usePathname();

  useHotkeys(`mod+shift+${MAIN_NAV_SHORTCUTS.dashboard}`, (event) => {
    event.preventDefault();
    router.push(routes.app.dashboard.Index);
  });

  useHotkeys(`mod+shift+${MAIN_NAV_SHORTCUTS.students}`, (event) => {
    event.preventDefault();
    router.push(routes.app.students.Index);
  });

  useHotkeys(`mod+shift+${MAIN_NAV_SHORTCUTS.calendar}`, (event) => {
    event.preventDefault();
    router.push(routes.app.calendar.Index);
  });

  useHotkeys(`mod+shift+${MAIN_NAV_SHORTCUTS.cases}`, (event) => {
    event.preventDefault();
    router.push(routes.app.cases.Index);
  });

  return (
    <SidebarGroup>
      <SidebarMenu className="text-muted-foreground">
        {MAIN_NAV_ITEMS.map((item) => {
          const isActive = pathname === getPathname(item.url, baseUrl.App);

          return (
            <SidebarMenuItem className="group" key={item.title}>
              <Link href={item.url}>
                <SidebarMenuButton
                  className="group/menu-button cursor-pointer"
                  isActive={isActive}
                  tooltip={item.title}
                >
                  {item.icon}
                  <span>{item.title}</span>
                  <SidebarMenuBadge className="hidden group-hover/menu-button:block">
                    {item.shortcut}
                  </SidebarMenuBadge>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
