'use client';

import { routes } from '@lilypad/shared/routes';
import { Kbd, Kbd<PERSON><PERSON> } from '@lilypad/ui/components/kbd';
import {
  SidebarGroup,
  type SidebarGroupProps,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@lilypad/ui/components/sidebar';
import { AnimatedMessageCircleIcon, AnimatedPlusIcon } from '@lilypad/ui/icons';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { FeedbackDialog } from '@/features/feedback';

export type NavSupportProps = SidebarGroupProps & {};

const SUPPORT_NAV_SHORTCUTS = {
  addStudent: 'a',
  feedback: 'f',
};

export function NavSupport({ ...props }: NavSupportProps): React.JSX.Element {
  const { setFeedbackOpen } = useSidebar();
  const router = useRouter();
  
  const handleAddStudent = React.useCallback((): void => {
    router.push(routes.app.students.Add);
  }, [router]);

  useHotkeys(`mod+shift+${SUPPORT_NAV_SHORTCUTS.addStudent}`, (event) => {
    event.preventDefault();
    handleAddStudent();
  });

  useHotkeys(`mod+shift+${SUPPORT_NAV_SHORTCUTS.feedback}`, (event) => {
    event.preventDefault();
    setFeedbackOpen(true);
  });

  return (
    <SidebarGroup {...props}>
      <SidebarMenu>
        <FeedbackDialog>
          <SidebarMenuItem>
            <SidebarMenuButton
              className="group/menu-button cursor-pointer whitespace-nowrap text-muted-foreground"
              tooltip="Feedback"
              type="button"
            >
              <AnimatedMessageCircleIcon />
              <span>Share feedback</span>
              <SidebarMenuBadge className="hidden group-hover/menu-button:block">
                <Kbd size="sm" variant="ghost">
                  <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
                  <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
                  <KbdKey>
                    {SUPPORT_NAV_SHORTCUTS.feedback.toUpperCase()}
                  </KbdKey>
                </Kbd>
              </SidebarMenuBadge>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </FeedbackDialog>

        <SidebarMenuItem>
          <SidebarMenuButton
            onClick={handleAddStudent}
            className="group/menu-button cursor-pointer whitespace-nowrap"
            tooltip="Add student / Referral"
            type="button"
            variant="primary"
          >
            <AnimatedPlusIcon />
            <span>Add student / Referral</span>
            <SidebarMenuBadge className="hidden group-hover/menu-button:block">
              <Kbd size="sm" className='text-background'>
                <KbdKey className="mt-0.25 text-sm">⇧</KbdKey>
                <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
                <KbdKey>
                  {SUPPORT_NAV_SHORTCUTS.addStudent.toUpperCase()}
                </KbdKey>
              </Kbd>
            </SidebarMenuBadge>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}
