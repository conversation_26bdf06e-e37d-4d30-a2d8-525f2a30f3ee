'use client';

import { Input } from '@lilypad/ui/components/input';
import { cn } from '@lilypad/ui/lib/utils';
import React, { useCallback, useEffect, useRef, useState } from 'react';

interface StudentInputCellProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  placeholder?: string;
  hasError?: boolean;
  dataRow?: number;
  dataCol?: string;
  className?: string; 
  disabled?: boolean;
}

const DEBOUNCE_DELAY = 300;

export const StudentInputCell = React.memo(function StudentInputCellComponent({
  value,
  onChange,
  onKeyDown,
  placeholder,
  hasError,
  dataRow,
  dataCol,
  className,
  disabled,
}: StudentInputCellProps) {
  const [localValue, setLocalValue] = useState(value);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastEmittedValueRef = useRef(value);

  useEffect(() => {
    if (value !== lastEmittedValueRef.current) {
      setLocalValue(value);
    }
  }, [value]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalValue(newValue);

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout for debounced update
      timeoutRef.current = setTimeout(() => {
        if (newValue !== lastEmittedValueRef.current) {
          lastEmittedValueRef.current = newValue;
          onChange(newValue);
        }
      }, DEBOUNCE_DELAY);
    },
    [onChange]
  );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleBlur = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (localValue !== lastEmittedValueRef.current) {
      lastEmittedValueRef.current = localValue;
      onChange(localValue);
    }
  }, [localValue, onChange]);

  return (
    <Input
      className={cn(
        'h-8 w-32 px-2',
        hasError && 'border-destructive focus:ring-destructive',
        className
      )}
      data-col={dataCol}
      data-row={dataRow}
      onBlur={handleBlur}
      onChange={handleChange}
      onKeyDown={onKeyDown}
      placeholder={placeholder}
      value={localValue}
      disabled={disabled}
    />
  );
});
