'use client';

import { Toggle } from '@lilypad/ui/components/toggle';
import { cn } from '@lilypad/ui/lib/utils';
import { SquareCheckIcon, SquareDashedIcon } from 'lucide-react';

interface OutOfDistrictToggleCellProps {
	value: boolean;
	onChange: (value: boolean) => void;
	hasError: boolean;
}

export function OutOfDistrictToggleCell({
	value,
	onChange,
	hasError,
}: OutOfDistrictToggleCellProps) {
	return (
		<div className="flex items-center justify-center">
		<Toggle
			className={cn(
				'h-8 w-fit px-2 data-[state=on]:border-primary',
				hasError && 'border-destructive'
			)}
			onPressedChange={onChange}
			pressed={value}
			variant="outline"
		>
			{value ? (
				<SquareCheckIcon className="size-4 text-primary" />
			) : (
					<SquareDashedIcon className="size-4 text-muted-foreground" />
				)}
			</Toggle>
		</div>
	);
}
