'use client';

import { DocumentCategoryEnum, DocumentCategoryEnumMap } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@lilypad/ui/components/dialog';
import { If } from '@lilypad/ui/components/if';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { toast } from '@lilypad/ui/components/sonner';
import { cn } from '@lilypad/ui/lib/utils';
import { FilePdfIcon } from '@lilypad/ui/icons';
import {
  FileIcon,
  FileImageIcon,
  FileSpreadsheetIcon,
  FileTextIcon,
  InfoIcon,
  Trash2Icon,
  UploadIcon,
} from 'lucide-react';
import React, { useRef, useState } from 'react';
import type { DocumentSchema } from '@lilypad/api/schemas/students';

interface DocumentUploadCellProps {
  value: DocumentSchema[];
  onChange: (value: DocumentSchema[]) => void;
}

const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

const ALLOWED_EXTENSIONS = ['.pdf', '.xls', '.xlsx', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.webp'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const DocumentUploadCell = React.memo(
  function DocumentUploadCellComponent({
    value,
    onChange,
  }: DocumentUploadCellProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [dragActive, setDragActive] = React.useState(false);

    const hasBackgroundInfo = value.some(
      (doc) => doc.category === DocumentCategoryEnum.BACKGROUND
    );

    const getFileIcon = (
      file: File | { type: string; name: string; size: number; data: string }
    ) => {
      const fileType = file instanceof File ? file.type : file.type;

      if (fileType === 'application/pdf') {
        return FilePdfIcon;
      }
      if (fileType.includes('spreadsheet') || fileType.includes('excel')) {
        return FileSpreadsheetIcon;
      }
      if (fileType.includes('word') || fileType === 'text/plain') {
        return FileTextIcon;
      }
      if (fileType.includes('image/')) {
        return FileImageIcon;
      }
      return FileIcon;
    };

    const formatFileSize = (bytes: number) => {
      if (bytes === 0) {
        return '0 Bytes';
      }
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
    };



    const handleFileSelect = (files: FileList) => {
      const newDocuments: DocumentSchema[] = [];
      const errors: string[] = [];

      for (const file of Array.from(files)) {
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          console.error(`File type ${file.type} not supported`);
          errors.push(`${file.name}: File type not supported`);
          continue;
        }

        if (file.size > MAX_FILE_SIZE) {
          console.error(`File ${file.name} is too large. Maximum size is 5MB.`);
          errors.push(`${file.name}: File too large (max 5MB)`);
          continue;
        }

        newDocuments.push({
          file,
          category: DocumentCategoryEnum.BACKGROUND,
        });
      }

      if (errors.length > 0) {
        toast.error(errors.join('\n'));
      }

      if (newDocuments.length > 0) {
        onChange([...value, ...newDocuments]);
      }
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleFileSelect(e.dataTransfer.files);
      }
    };

    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(true);
    };

    const handleDragLeave = () => {
      setDragActive(false);
    };

    const removeDocument = (index: number) => {
      const newDocs = [...value];
      newDocs.splice(index, 1);
      onChange(newDocs);
    };

    const updateDocumentCategory = (index: number, category: DocumentCategoryEnum) => {
      const newDocs = [...value];
      newDocs[index] = { ...newDocs[index], category };
      onChange(newDocs);
    };

    return (
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogTrigger asChild>
          <Button
            className="h-8"
            onClick={() => setIsDialogOpen(true)}
            size="sm"
            variant="outline"
          >
            <FileTextIcon className="mr-2 size-4" />
            {value.length > 0 ? `${value.length} Files` : 'Upload'}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Upload Documents</DialogTitle>
            <DialogDescription>
              Upload student documents. Maximum 5MB per file.
              <br />
              <strong>Allowed formats:</strong> PDF, Excel, Word, Text, Images
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Background Information Warning */}
            <If condition={!hasBackgroundInfo}>
              <div className="flex items-start gap-3 rounded-lg border border-dashed bg-muted/50 p-4 text-muted-foreground">
                <InfoIcon className="mt-0.5 size-5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Background Information Required</p>
                  <p className="mt-1 text-xs">
                    To activate the case, please upload a document and assign it the "Background" category. Background information is required and must be provided if available.
                  </p>
                </div>
              </div>
            </If>

            {/* Drop Zone */}
            {/** biome-ignore lint/a11y/noStaticElementInteractions: Fix later */}
            {/** biome-ignore lint/nursery/noNoninteractiveElementInteractions: Fix later */}
            <div
              className={cn(
                'rounded-lg border border-dashed p-8 text-center transition-colors',
                dragActive ? 'border-primary bg-primary/5' : 'border-border',
                'cursor-pointer hover:border-primary hover:bg-primary/5'
              )}
              onClick={() => fileInputRef.current?.click()}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <UploadIcon className="mx-auto mb-2 size-8 text-muted-foreground" />
              <p className="font-medium text-sm">
                Drag and drop files or click to browse
              </p>
              <p className="mt-1 text-muted-foreground text-xs">
                PDF, Excel, Word, Text, or Image files up to 5MB
              </p>
              <input
                accept={ALLOWED_EXTENSIONS.join(',')}
                className="hidden"
                multiple
                onChange={(e) =>
                  e.target.files && handleFileSelect(e.target.files)
                }
                ref={fileInputRef}
                type="file"
              />
            </div>

            {/* File List */}
            <If condition={value.length > 0}>
              <div className="space-y-2">
                <p className="font-medium text-sm">
                  Uploaded Documents{' '}
                  <span className="rounded-md bg-muted px-2 py-1 text-muted-foreground text-xs">
                    {value.length}
                    {value.length === 1 ? ' document' : ' documents'}
                  </span>
                </p>
                <ScrollArea className="h-[200px]">
                  <div className="space-y-2">
                    {value.map((doc, index) => {
                      const Icon = getFileIcon(doc.file);
                      return (
                        <Card key={`${doc.file.name}-${doc.file.size}-${index}`} className="rounded-md py-2 shadow-none">
                          <CardContent className="flex items-center gap-4 pr-1 pl-3">
                            <Icon className="size-4 flex-shrink-0 text-muted-foreground" />

                            <div className="flex min-w-0 flex-1 flex-col gap-1">
                              <p className="truncate font-medium text-sm">{doc.file.name}</p>
                              <p className="text-muted-foreground text-xs">
                                {formatFileSize(doc.file.size)} • {new Date().toLocaleDateString()}
                              </p>
                            </div>

                            <div className="flex items-center gap-2">
                              <Select
                                onValueChange={(category: DocumentCategoryEnum) => updateDocumentCategory(index, category)}
                                value={doc.category}
                              >
                                <SelectTrigger className="w-40" size="sm">
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.entries(DocumentCategoryEnumMap).map(([key, label]) => (
                                    <SelectItem key={key} value={key}>
                                      {label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>

                              <Button
                                className="group size-8 flex-shrink-0 p-0"
                                onClick={() => removeDocument(index)}
                                size="sm"
                                type="button"
                                variant="cancel"
                              >
                                <Trash2Icon className="size-4 text-muted-foreground group-hover:text-destructive" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </ScrollArea>
              </div>
            </If>

            <If condition={!value.length}>
              <p className="text-center text-muted-foreground text-sm">
                No documents uploaded yet.
              </p>
            </If>
          </div>

          <DialogFooter>
            <Button onClick={() => setIsDialogOpen(false)} variant="outline">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
);
