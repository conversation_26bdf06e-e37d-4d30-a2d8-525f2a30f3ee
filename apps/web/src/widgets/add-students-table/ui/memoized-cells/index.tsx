export { MemoizedDateCell } from './memoized-date-cell';
export { MemoizedPrivateSchoolCell } from './memoized-private-school-cell';
export { MemoizedSelectCell } from './memoized-select-cell';
export { MemoizedStudentInputCell } from './memoized-student-input-cell';
export { MemoizedToggleCell } from './memoized-toggle-cell';

import type { DocumentCategoryEnum } from '@lilypad/db/schema/enums';
import type { Language } from '@lilypad/db/schema/types';
import { Button } from '@lilypad/ui/components/button';
import { Checkbox } from '@lilypad/ui/components/checkbox';
import { cn } from '@lilypad/ui/lib/utils';
import { Users } from 'lucide-react';
import React from 'react';
import {
  useStudentField,
  useStudentOperations,
} from '@/features/add-students/model/hooks/use-student-data';
import { useStudentsDataStore } from '@/features/add-students/model/stores/students-data.store';
import { useSelectionStore } from '@/features/add-students/model/stores/students-selection.store';
import { useValidationStore } from '@/features/add-students/model/stores/students-validation.store';
import { LanguageMultiselect } from '@/shared/ui/students/languages/language-multi-select';
import { DocumentUploadCell } from '../cells/document-upload-cell';
import { RowActionsCell } from '../cells/row-actions-cell';

interface MemoizedCellProps {
  studentId: string;
}

export const SelectionHeader = React.memo(function SelectionHeaderComponent() {
  const selectedCount = useSelectionStore((state) => state.selectedIds.size);
  const studentsCount = useStudentsDataStore((state) => state.students.size);
  const toggleAll = useSelectionStore((state) => state.toggleAllSelection);

  const isAllSelected = selectedCount === studentsCount && studentsCount > 0;
  const _isIndeterminate = selectedCount > 0 && selectedCount < studentsCount;

  const handleToggleAll = React.useCallback(() => {
    // Get current student IDs at the time of click
    const currentStudentIds = Array.from(
      useStudentsDataStore.getState().students.keys()
    );
    toggleAll(currentStudentIds);
  }, [toggleAll]);

  return (
    <Checkbox
      aria-label="Select all"
      // indeterminate={isIndeterminate}
      checked={isAllSelected}
      onCheckedChange={handleToggleAll}
    />
  );
});

export const SelectionCell = React.memo(function SelectionCellComponent({
  studentId,
}: MemoizedCellProps) {
  const isSelected = useSelectionStore((state) => state.isSelected(studentId));
  const toggleSelection = useSelectionStore((state) => state.toggleSelection);

  return (
    <Checkbox
      aria-label="Select row"
      checked={isSelected}
      onCheckedChange={() => toggleSelection(studentId)}
    />
  );
});

interface MemoizedLanguageCellProps extends MemoizedCellProps {
  languages: Pick<Language, 'id' | 'name' | 'emoji'>[];
}

export const MemoizedLanguageCell = React.memo(
  function MemoizedLanguageCellComponent({
    studentId,
    languages,
  }: MemoizedLanguageCellProps) {
    const { updateStudent } = useStudentOperations();
    const languageIds =
      (useStudentField(studentId, 'languageIds') as string[]) || [];
    const primaryLanguageId = useStudentField(studentId, 'primaryLanguageId') as
      | string
      | null;
    const errors = useValidationStore((state) => state.getErrors(studentId));
    const hasError = !!(errors?.languageIds || errors?.primaryLanguageId);

    const handleChange = React.useCallback(
      (newLanguageIds: string[], newPrimaryLanguageId: string | null) => {
        updateStudent(studentId, {
          languageIds: newLanguageIds,
          primaryLanguageId: newPrimaryLanguageId,
        });
      },
      [studentId, updateStudent]
    );

    return (
      <LanguageMultiselect
        hasError={hasError}
        languages={languages}
        onChange={handleChange}
        primaryLanguageId={primaryLanguageId}
        size="sm"
        value={languageIds}
      />
    );
  }
);

interface MemoizedParentsCellProps extends MemoizedCellProps {
  onOpenDialog: (state: { open: boolean; studentId: string | null }) => void;
}

export const MemoizedParentsCell = React.memo(
  function MemoizedParentsCellComponent({
    studentId,
    onOpenDialog,
  }: MemoizedParentsCellProps) {
    const parents = useStudentField(studentId, 'parents') || [];
    const errors = useValidationStore((state) => state.getErrors(studentId));
    const hasError = !!errors?.parents;

    const handleClick = React.useCallback(() => {
      onOpenDialog({ open: true, studentId });
    }, [onOpenDialog, studentId]);

    return (
      <Button
        className={cn('h-8', hasError && 'border-destructive')}
        onClick={handleClick}
        size="sm"
        variant="outline"
      >
        <Users className="mr-2 size-4" />
        {Array.isArray(parents) && parents.length > 0 ? (
          <span>{parents.length} Added</span>
        ) : (
          <span>Add Parents</span>
        )}
      </Button>
    );
  }
);

export const MemoizedDocumentsCell = React.memo(
  function MemoizedDocumentsCellComponent({ studentId }: MemoizedCellProps) {
    const { updateStudent } = useStudentOperations();
    const documents = useStudentField(studentId, 'documents') || [];

    const handleChange = React.useCallback(
      (newDocuments: { file: File; category: DocumentCategoryEnum }[]) => {
        updateStudent(studentId, { documents: newDocuments });
      },
      [studentId, updateStudent]
    );

    return (
      <DocumentUploadCell
        onChange={handleChange}
        value={Array.isArray(documents) ? documents : []}
      />
    );
  }
);

export const MemoizedActionsCell = React.memo(
  function MemoizedActionsCellComponent({ studentId }: MemoizedCellProps) {
    const { deleteStudent, duplicateStudent } = useStudentOperations();

    const handleDuplicate = React.useCallback(() => {
      duplicateStudent(studentId);
    }, [duplicateStudent, studentId]);

    const handleDelete = React.useCallback(() => {
      deleteStudent(studentId);
    }, [deleteStudent, studentId]);

    return (
      <RowActionsCell onDelete={handleDelete} onDuplicate={handleDuplicate} />
    );
  }
);
