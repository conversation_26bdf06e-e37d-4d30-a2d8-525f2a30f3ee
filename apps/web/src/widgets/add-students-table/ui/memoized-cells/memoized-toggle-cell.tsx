import React from 'react';
import {
	useStudentField,
	useStudentOperations,
} from '@/features/add-students/model/hooks/use-student-data';
import type { StudentRowData } from '@/features/add-students/model/schema';
import { useValidationStore } from '@/features/add-students/model/stores/students-validation.store';

interface MemoizedToggleCellProps {
	studentId: string;
	field: keyof StudentRowData;
	renderCell: (props: {
		value: boolean;
		onChange: (value: boolean) => void;
		hasError: boolean;
	}) => React.ReactNode;
	onToggle?: (value: boolean, studentId: string) => void;
}

export const MemoizedToggleCell = React.memo(
	function MemoizedToggleCellComponent({
		studentId,
		field,
		renderCell,
		onToggle,
	}: MemoizedToggleCellProps) {
		const value = (useStudentField(studentId, field) as boolean);
		const { updateStudent } = useStudentOperations();
		const errors = useValidationStore((state) => state.getErrors(studentId));
		const hasError = !!errors?.[field];

		const handleChange = React.useCallback(
			(newValue: boolean) => {
				updateStudent(studentId, { [field]: newValue });
				onToggle?.(newValue, studentId);
			},
			[studentId, field, updateStudent, onToggle]
		);

		return <>{renderCell({ value, onChange: handleChange, hasError })}</>;
	}
);
