import { Input } from '@lilypad/ui/components/input';
import { cn } from '@lilypad/ui/lib/utils';
import React from 'react';
import {
  useStudentField,
  useStudentOperations,
} from '@/features/add-students/model/hooks/use-student-data';
import { useValidationStore } from '@/features/add-students/model/stores/students-validation.store';

interface MemoizedPrivateSchoolCellProps {
  studentId: string;
  dataRow: number;
  dataCol: string;
  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

export const MemoizedPrivateSchoolCell = React.memo(
  function MemoizedPrivateSchoolCellComponent({
    studentId,
    dataRow,
    dataCol,
    onKeyDown,
  }: MemoizedPrivateSchoolCellProps) {
    const privateSchool = (useStudentField(studentId, 'privateSchool') as string) || '';
    const outOfDistrict = (useStudentField(studentId, 'outOfDistrict') as boolean);
    const { updateStudent } = useStudentOperations();
    const errors = useValidationStore((state) => state.getErrors(studentId));
    const hasError = !!errors?.privateSchool;

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        updateStudent(studentId, { privateSchool: value });
      },
      [studentId, updateStudent]
    );

    // Clear private school when out of district is turned off
    React.useEffect(() => {
      if (!outOfDistrict && privateSchool) {
        updateStudent(studentId, { privateSchool: '' });
      }
    }, [outOfDistrict, privateSchool, studentId, updateStudent]);

    return (
      <Input
        className={cn(
          'h-8 w-48 text-sm',
          hasError && 'border-destructive',
          !outOfDistrict && 'cursor-not-allowed bg-muted text-muted-foreground'
        )}
        data-col={dataCol}
        data-row={dataRow}
        disabled={!outOfDistrict}
        onChange={handleChange}
        onKeyDown={onKeyDown}
        placeholder={outOfDistrict ? 'Enter private school' : 'Out of district required'}
        value={privateSchool}
      />
    );
  }
);
